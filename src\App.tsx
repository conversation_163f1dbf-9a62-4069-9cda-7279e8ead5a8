import React, { useState, useEffect } from 'react';
import { database } from './services/database';
import { useSettings } from './hooks/useDatabase';
import { Page } from './types';

// Layout Components
import Sidebar from './components/Layout/Sidebar';
import Header from './components/Layout/Header';

// Page Components
import Dashboard from './components/Dashboard/Dashboard';
import EmployeeList from './components/Employees/EmployeeList';
import AttendanceList from './components/Attendance/AttendanceList';
import PayrollList from './components/Payroll/PayrollList';
import ServiceManagement from './components/Services/ServiceManagement';
import BookingManagement from './components/Bookings/BookingManagement';
import CustomerManagement from './components/Customers/CustomerManagement';
import FinancialDashboard from './components/Financial/FinancialDashboard';
import Settings from './components/Settings/Settings';

// Color mappings for primary color system
const colorMappings = {
  blue: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },
  indigo: {
    50: '#eef2ff',
    100: '#e0e7ff',
    200: '#c7d2fe',
    300: '#a5b4fc',
    400: '#818cf8',
    500: '#6366f1',
    600: '#4f46e5',
    700: '#4338ca',
    800: '#3730a3',
    900: '#312e81',
  },
  purple: {
    50: '#faf5ff',
    100: '#f3e8ff',
    200: '#e9d5ff',
    300: '#d8b4fe',
    400: '#c084fc',
    500: '#a855f7',
    600: '#9333ea',
    700: '#7c3aed',
    800: '#6b21a8',
    900: '#581c87',
  },
  pink: {
    50: '#fdf2f8',
    100: '#fce7f3',
    200: '#fbcfe8',
    300: '#f9a8d4',
    400: '#f472b6',
    500: '#ec4899',
    600: '#db2777',
    700: '#be185d',
    800: '#9d174d',
    900: '#831843',
  },
  red: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
  orange: {
    50: '#fff7ed',
    100: '#ffedd5',
    200: '#fed7aa',
    300: '#fdba74',
    400: '#fb923c',
    500: '#f97316',
    600: '#ea580c',
    700: '#c2410c',
    800: '#9a3412',
    900: '#7c2d12',
  },
  amber: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  yellow: {
    50: '#fefce8',
    100: '#fef9c3',
    200: '#fef08a',
    300: '#fde047',
    400: '#facc15',
    500: '#eab308',
    600: '#ca8a04',
    700: '#a16207',
    800: '#854d0e',
    900: '#713f12',
  },
  lime: {
    50: '#f7fee7',
    100: '#ecfccb',
    200: '#d9f99d',
    300: '#bef264',
    400: '#a3e635',
    500: '#84cc16',
    600: '#65a30d',
    700: '#4d7c0f',
    800: '#365314',
    900: '#1a2e05',
  },
  green: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  emerald: {
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981',
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b',
  },
  teal: {
    50: '#f0fdfa',
    100: '#ccfbf1',
    200: '#99f6e4',
    300: '#5eead4',
    400: '#2dd4bf',
    500: '#14b8a6',
    600: '#0d9488',
    700: '#0f766e',
    800: '#115e59',
    900: '#134e4a',
  },
  cyan: {
    50: '#ecfeff',
    100: '#cffafe',
    200: '#a5f3fc',
    300: '#67e8f9',
    400: '#22d3ee',
    500: '#06b6d4',
    600: '#0891b2',
    700: '#0e7490',
    800: '#155e75',
    900: '#164e63',
  },
  sky: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  slate: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  },
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
};

function App() {
  const [currentPage, setCurrentPage] = useState<Page>('dashboard');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [initializationAttempted, setInitializationAttempted] = useState(false);
  
  const { settings, loading: settingsLoading } = useSettings(isInitialized);

  // Initialize database only once
  useEffect(() => {
    if (initializationAttempted) return;
    
    const initDatabase = async () => {
      try {
        setInitializationAttempted(true);
        await database.init();
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize database:', error);
        setInitializationAttempted(false); // Allow retry on error
      }
    };

    initDatabase();
  }, [initializationAttempted]);

  // Handle responsive behavior
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 1024;
      setIsMobile(mobile);
      if (mobile) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Apply dark mode, RTL mode, language, and primary color from settings
  useEffect(() => {
    const htmlElement = document.documentElement;
    
    if (settings?.darkMode) {
      htmlElement.classList.add('dark');
    } else {
      htmlElement.classList.remove('dark');
    }

    // Handle RTL mode - automatically enable for Arabic or manual setting
    const shouldUseRTL = settings?.language === 'ar' || settings?.rtlMode;
    if (shouldUseRTL) {
      htmlElement.setAttribute('dir', 'rtl');
      htmlElement.classList.add('rtl');
    } else {
      htmlElement.setAttribute('dir', 'ltr');
      htmlElement.classList.remove('rtl');
    }

    // Apply language attribute
    if (settings?.language) {
      htmlElement.setAttribute('lang', settings.language);
    }

    // Apply primary color CSS custom properties
    if (settings?.primaryColor && colorMappings[settings.primaryColor as keyof typeof colorMappings]) {
      const colors = colorMappings[settings.primaryColor as keyof typeof colorMappings];
      
      // Set CSS custom properties for the primary color
      Object.entries(colors).forEach(([shade, value]) => {
        htmlElement.style.setProperty(`--primary-${shade}`, value);
      });
      
      // Set main primary color variable
      htmlElement.style.setProperty('--primary-color', colors[600]);
      htmlElement.style.setProperty('--primary-color-hover', colors[700]);
      htmlElement.style.setProperty('--primary-color-light', colors[100]);
      htmlElement.style.setProperty('--primary-color-dark', colors[800]);
    }
  }, [settings?.darkMode, settings?.rtlMode, settings?.language, settings?.primaryColor]);

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'employees':
        return <EmployeeList />;
      case 'attendance':
        return <AttendanceList />;
      case 'payroll':
        return <PayrollList />;
      case 'services':
        return <ServiceManagement />;
      case 'bookings':
        return <BookingManagement />;
      case 'customers':
        return <CustomerManagement />;
      case 'financial':
        return <FinancialDashboard />;
      case 'settings':
        return <Settings />;
      default:
        return <Dashboard />;
    }
  };

  if (!isInitialized || settingsLoading) {
    return (
      <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Initializing SalonSys...</p>
        </div>
      </div>
    );
  }

  const shouldUseRTL = settings?.language === 'ar' || settings?.rtlMode;

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${shouldUseRTL ? 'rtl' : 'ltr'}`}>
      <Sidebar
        currentPage={currentPage}
        onPageChange={setCurrentPage}
        isCollapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        isMobile={isMobile}
        rtlMode={shouldUseRTL}
      />

      <div className={`flex-1 transition-all duration-300 ${
        shouldUseRTL 
          ? (sidebarCollapsed ? 'lg:mr-16' : 'lg:mr-64')
          : (sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64')
      }`}>
        <Header
          darkMode={settings?.darkMode || false}
          onToggleDarkMode={() => {}} // Removed toggle functionality from header
          sidebarCollapsed={sidebarCollapsed}
          rtlMode={shouldUseRTL}
        />

        <main className="p-4 sm:p-6">
          {renderCurrentPage()}
        </main>
      </div>
    </div>
  );
}

export default App;