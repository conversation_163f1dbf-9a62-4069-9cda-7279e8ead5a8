import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { useServices, useServicePackages, useSettings } from '../../hooks/useDatabase';
import { useTranslation } from '../../utils/translations';
import { ServicePackage } from '../../types';

interface PackageFormProps {
  package?: ServicePackage | null;
  onClose: () => void;
}

export default function PackageForm({ package: pkg, onClose }: PackageFormProps) {
  const { services } = useServices();
  const { addPackage, updatePackage } = useServicePackages();
  const { settings } = useSettings(true);
  const t = useTranslation(settings?.language || 'en');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    totalPrice: '',
    discountedPrice: '',
    selectedServices: [] as string[],
  });

  useEffect(() => {
    if (pkg) {
      setFormData({
        name: pkg.name,
        description: pkg.description,
        totalPrice: pkg.totalPrice.toString(),
        discountedPrice: pkg.discountedPrice.toString(),
        selectedServices: pkg.services,
      });
    }
  }, [pkg]);

  const handleServiceToggle = (serviceId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedServices: prev.selectedServices.includes(serviceId)
        ? prev.selectedServices.filter(id => id !== serviceId)
        : [...prev.selectedServices, serviceId]
    }));
  };

  const calculateTotalPrice = () => {
    return formData.selectedServices.reduce((total, serviceId) => {
      const service = services.find(s => s.id === serviceId);
      return total + (service ? service.price : 0);
    }, 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const packageData = {
      name: formData.name,
      description: formData.description,
      services: formData.selectedServices,
      totalPrice: parseFloat(formData.totalPrice) || calculateTotalPrice(),
      discountedPrice: parseFloat(formData.discountedPrice),
      isActive: true,
    };

    if (pkg) {
      await updatePackage(pkg.id, packageData);
    } else {
      await addPackage(packageData);
    }
    
    onClose();
  };

  const isRTL = settings?.language === 'ar' || settings?.rtlMode;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {pkg ? t.editPackage : t.addNewPackage}
          </h2>
          <button onClick={onClose} className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t.packageName}
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              required
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
            />
          </div>

          <div>
            <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t.description}
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
            />
          </div>

          <div>
            <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t.selectServices}
            </label>
            <div className="space-y-2 max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg p-3">
              {services.map(service => (
                <label key={service.id} className={`flex items-center space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  <input
                    type="checkbox"
                    checked={formData.selectedServices.includes(service.id)}
                    onChange={() => handleServiceToggle(service.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {service.name}
                    </span>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {service.duration}{t.minutes.charAt(0)} - ${service.price}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.totalPrice}
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.totalPrice || calculateTotalPrice()}
                onChange={(e) => setFormData(prev => ({ ...prev, totalPrice: e.target.value }))}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder={calculateTotalPrice().toString()}
              />
              <p className={`text-xs text-gray-500 mt-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.autoCalculated}: ${calculateTotalPrice()}
              </p>
            </div>

            <div>
              <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.packagePrice}
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.discountedPrice}
                onChange={(e) => setFormData(prev => ({ ...prev, discountedPrice: e.target.value }))}
                required
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
              />
            </div>
          </div>

          {formData.selectedServices.length > 0 && (
            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
              <h4 className={`text-sm font-medium text-blue-800 dark:text-blue-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.selectedServices} ({formData.selectedServices.length})
              </h4>
              <div className="space-y-1">
                {formData.selectedServices.map(serviceId => {
                  const service = services.find(s => s.id === serviceId);
                  return service ? (
                    <div key={serviceId} className={`text-xs text-blue-600 dark:text-blue-400 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {service.name} - {service.duration}{t.minutes.charAt(0)} - ${service.price}
                    </div>
                  ) : null;
                })}
              </div>
              <div className={`mt-2 pt-2 border-t border-blue-200 dark:border-blue-700 ${isRTL ? 'text-right' : 'text-left'}`}>
                <div className="text-sm text-blue-800 dark:text-blue-300">
                  {t.totalValue}: ${calculateTotalPrice()}
                </div>
                {formData.discountedPrice && (
                  <div className="text-sm text-green-600 dark:text-green-400">
                    {t.savings}: ${calculateTotalPrice() - parseFloat(formData.discountedPrice)}
                  </div>
                )}
              </div>
            </div>
          )}

          <div className={`flex justify-end space-x-3 pt-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              {t.cancel}
            </button>
            <button
              type="submit"
              disabled={formData.selectedServices.length === 0}
              className="px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {pkg ? t.updatePackage : t.addPackage}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}