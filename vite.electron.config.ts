import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  build: {
    lib: {
      entry: {
        main: resolve(__dirname, 'electron/main.ts'),
        preload: resolve(__dirname, 'electron/preload.ts'),
      },
      formats: ['cjs'],
    },
    outDir: 'dist-electron',
    emptyOutDir: true,
    rollupOptions: {
      external: [
        'electron',
        'better-sqlite3',
        'fs',
        'path',
        'os',
        'url',
        'node:url',
        'node:path',
        'node:fs',
        'node:os',
      ],
      output: {
        entryFileNames: '[name].cjs',
        format: 'cjs',
      },
    },
    target: 'node18',
    minify: false,
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
});
