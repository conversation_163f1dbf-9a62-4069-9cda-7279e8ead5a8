import React, { useState } from 'react';
import { X, DollarSign } from 'lucide-react';
import { Employee } from '../../types';
import { useAdvances, useSettings } from '../../hooks/useDatabase';
import { useTranslation } from '../../utils/translations';
import { format } from 'date-fns';

interface RequestAdvanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  employees: Employee[];
}

export default function RequestAdvanceModal({ isOpen, onClose, employees }: RequestAdvanceModalProps) {
  const { addAdvance } = useAdvances();
  const { settings } = useSettings(true);
  const t = useTranslation(settings?.language || 'en');
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [amount, setAmount] = useState('');
  const [reason, setReason] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedEmployee && amount && reason) {
      await addAdvance({
        employeeId: selectedEmployee,
        amount: parseFloat(amount),
        reason,
        date: format(new Date(), 'yyyy-MM-dd'),
        status: 'active'
      });
      onClose();
      setSelectedEmployee('');
      setAmount('');
      setReason('');
    }
  };

  if (!isOpen) return null;

  const selectedEmp = employees.find(emp => emp.id === selectedEmployee);
  const isRTL = settings?.language === 'ar' || settings?.rtlMode;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-md">
        <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{t.requestEmployeeAdvance}</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <p className={`text-sm text-gray-600 dark:text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>
            {t.notAllowedDuringFirstWeek}
          </p>

          <div>
            <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t.selectEmployee}
            </label>
            <select
              value={selectedEmployee}
              onChange={(e) => setSelectedEmployee(e.target.value)}
              required
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
            >
              <option value="">{t.selectAnEmployee}</option>
              {employees.map(employee => (
                <option key={employee.id} value={employee.id}>
                  {employee.name} - ${employee.salary.toLocaleString()}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t.advanceAmount}
            </label>
            <div className="relative">
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                required
                min="1"
                step="0.01"
                placeholder="50"
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'pr-8 text-right' : 'pl-8 text-left'}`}
              />
              <DollarSign className={`absolute top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none ${isRTL ? 'right-3' : 'left-3'}`} />
            </div>
          </div>

          <div>
            <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t.reasonForAdvance}
            </label>
            <textarea
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              required
              rows={3}
              placeholder={t.enterReasonForAdvanceRequest}
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
            />
          </div>

          {selectedEmp && (
            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
              <p className={`text-sm text-blue-800 dark:text-blue-300 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.employee}: {selectedEmp.name}
              </p>
              <p className={`text-sm text-blue-600 dark:text-blue-400 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.monthlySalary}: ${selectedEmp.salary.toLocaleString()}
              </p>
              {amount && (
                <p className={`text-sm text-blue-600 dark:text-blue-400 ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.advancePercentage}: {((parseFloat(amount) / selectedEmp.salary) * 100).toFixed(1)}% {t.ofSalary}
                </p>
              )}
            </div>
          )}

          <div className={`flex justify-end space-x-3 pt-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              {t.cancel}
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors"
            >
              {t.processAdvance}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}