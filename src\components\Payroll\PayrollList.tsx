import React, { useState, useEffect } from 'react';
import { DollarSign, Download, RefreshCw, TrendingUp, TrendingDown, Users, AlertCircle, Zap } from 'lucide-react';
import { useEmployees, useAdvances, useAttendance, usePayroll, useSettings } from '../../hooks/useDatabase';
import { format } from 'date-fns';

export default function PayrollList() {
  const { employees, updateEmployee } = useEmployees();
  const { advances, updateAdvance } = useAdvances();
  const { attendance, updateAttendanceRecord } = useAttendance();
  const { payroll, generatePayroll, resetMonthlyPayroll } = usePayroll();
  const { settings } = useSettings(true);
  const [currentMonth] = useState(format(new Date(), 'yyyy-MM'));
  const [isGenerating, setIsGenerating] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [employeeStatuses, setEmployeeStatuses] = useState<Record<string, 'pending' | 'paid'>>({});
  const [lastResetDate, setLastResetDate] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'advances' | 'penalties' | 'overtime'>('advances');

  const activeEmployees = employees.filter(emp => emp.status === 'active');
  const currentMonthAdvances = advances.filter(advance => 
    advance.date.startsWith(currentMonth) && advance.status === 'active'
  );
  
  // Smart payroll calculation with attendance rules - FIXED OVERTIME CALCULATION
  const getCurrentSalary = (employeeId: string) => {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee || !settings) return 0;

    const employeeAdvances = currentMonthAdvances.filter(advance => advance.employeeId === employeeId);
    const totalEmployeeAdvances = employeeAdvances.reduce((sum, advance) => sum + advance.amount, 0);
    
    // Calculate attendance-based adjustments
    const employeeAttendance = attendance.filter(record => 
      record.employeeId === employeeId && 
      record.date.startsWith(currentMonth)
    );

    // FIXED: Calculate late penalties based on thresholds
    const lateDays = employeeAttendance.filter(record => record.status === 'late').length;
    const lateDaysThreshold = settings.attendanceRules.lateDaysForPenalty || 3;
    const latePenalties = lateDays > lateDaysThreshold ? 
      (lateDays - lateDaysThreshold) * settings.attendanceRules.latePenalty : 0;

    // FIXED: Calculate absence penalties (only for working days) based on thresholds
    const absentDays = employeeAttendance.filter(record => {
      if (record.status !== 'absent') return false;
      
      const recordDate = new Date(record.date);
      const dayOfWeek = format(recordDate, 'EEEE').toLowerCase();
      return settings.workingDays.includes(dayOfWeek);
    }).length;
    const absentDaysThreshold = settings.attendanceRules.absentDaysForPenalty || 2;
    const absencePenalties = absentDays > absentDaysThreshold ? 
      (absentDays - absentDaysThreshold) * settings.attendanceRules.absencePenalty : 0;

    // FIXED: Calculate overtime bonuses - improved calculation
    const overtimeRecords = employeeAttendance.filter(record => 
      record.status === 'overtime' && record.checkIn && record.checkOut
    );
    
    const overtimeBonus = overtimeRecords.reduce((sum, record) => {
      if (!record.checkOut || !settings) return sum;
      
      const [workEndHour, workEndMin] = settings.workingHours.end.split(':').map(Number);
      const [checkOutHour, checkOutMin] = record.checkOut.split(':').map(Number);
      
      const workEndMinutes = workEndHour * 60 + workEndMin;
      const checkOutMinutes = checkOutHour * 60 + checkOutMin;
      
      const overtimeMinutes = Math.max(0, checkOutMinutes - workEndMinutes);
      const overtimeHours = overtimeMinutes / 60;
      
      return sum + (overtimeHours * settings.attendanceRules.overtimeReward);
    }, 0);

    // Calculate manual penalties from attendance notes
    const manualPenalties = employeeAttendance.reduce((sum, record) => {
      if (!record.notes) return sum;
      
      // Look for manual penalty patterns in notes
      const penaltyMatch = record.notes.match(/Manual penalty applied: \$(\d+(?:\.\d{2})?)/);
      if (penaltyMatch) {
        return sum + parseFloat(penaltyMatch[1]);
      }
      return sum;
    }, 0);

    return Math.max(0, employee.salary - totalEmployeeAdvances - latePenalties - absencePenalties - manualPenalties + overtimeBonus);
  };

  const totalCurrentPayroll = activeEmployees.reduce((sum, emp) => sum + getCurrentSalary(emp.id), 0);
  const totalAdvances = currentMonthAdvances.reduce((sum, advance) => sum + advance.amount, 0);
  
  // FIXED: Calculate total penalties and bonuses with thresholds
  const totalPenalties = activeEmployees.reduce((sum, emp) => {
    if (!settings) return sum;
    
    const employeeAttendance = attendance.filter(record => 
      record.employeeId === emp.id && 
      record.date.startsWith(currentMonth)
    );

    const lateDays = employeeAttendance.filter(record => record.status === 'late').length;
    const lateDaysThreshold = settings.attendanceRules.lateDaysForPenalty || 3;
    const latePenalties = lateDays > lateDaysThreshold ? 
      (lateDays - lateDaysThreshold) * settings.attendanceRules.latePenalty : 0;

    const absentDays = employeeAttendance.filter(record => {
      if (record.status !== 'absent') return false;
      const recordDate = new Date(record.date);
      const dayOfWeek = format(recordDate, 'EEEE').toLowerCase();
      return settings.workingDays.includes(dayOfWeek);
    }).length;
    const absentDaysThreshold = settings.attendanceRules.absentDaysForPenalty || 2;
    const absencePenalties = absentDays > absentDaysThreshold ? 
      (absentDays - absentDaysThreshold) * settings.attendanceRules.absencePenalty : 0;

    // Add manual penalties
    const manualPenalties = employeeAttendance.reduce((manualSum, record) => {
      if (!record.notes) return manualSum;
      const penaltyMatch = record.notes.match(/Manual penalty applied: \$(\d+(?:\.\d{2})?)/);
      if (penaltyMatch) {
        return manualSum + parseFloat(penaltyMatch[1]);
      }
      return manualSum;
    }, 0);

    return sum + latePenalties + absencePenalties + manualPenalties;
  }, 0);

  // FIXED: Calculate total overtime bonuses - improved calculation
  const totalOvertimeBonuses = activeEmployees.reduce((sum, emp) => {
    if (!settings) return sum;
    
    const employeeAttendance = attendance.filter(record => 
      record.employeeId === emp.id && 
      record.date.startsWith(currentMonth) &&
      record.status === 'overtime' &&
      record.checkOut
    );

    return sum + employeeAttendance.reduce((empSum, record) => {
      if (!record.checkOut || !settings) return empSum;
      
      const [workEndHour, workEndMin] = settings.workingHours.end.split(':').map(Number);
      const [checkOutHour, checkOutMin] = record.checkOut.split(':').map(Number);
      
      const workEndMinutes = workEndHour * 60 + workEndMin;
      const checkOutMinutes = checkOutHour * 60 + checkOutMin;
      
      const overtimeMinutes = Math.max(0, checkOutMinutes - workEndMinutes);
      const overtimeHours = overtimeMinutes / 60;
      
      return empSum + (overtimeHours * settings.attendanceRules.overtimeReward);
    }, 0);
  }, 0);
  
  const handleGeneratePayroll = async () => {
    setIsGenerating(true);
    try {
      await generatePayroll(currentMonth);
      alert('Payroll report generated successfully!');
    } catch (error) {
      console.error('Error generating payroll:', error);
      alert('Error generating payroll report. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleResetPayroll = async () => {
    if (confirm('Are you sure you want to reset the monthly payroll? This will mark all current month advances as processed, clear all penalties and overtime bonuses, and reset payroll calculations.')) {
      setIsResetting(true);
      try {
        // Mark all current month advances as processed
        for (const advance of currentMonthAdvances) {
          await updateAdvance(advance.id, {
            status: 'paid'
          });
        }
        
        // Clear all attendance penalties and overtime bonuses for current month
        const currentMonthAttendance = attendance.filter(record => 
          record.date.startsWith(currentMonth)
        );
        
        for (const record of currentMonthAttendance) {
          // Clear manual penalties from notes
          let updatedNotes = record.notes || '';
          if (updatedNotes.includes('Manual penalty applied:')) {
            updatedNotes = updatedNotes.replace(/Manual penalty applied: \$\d+(?:\.\d{2})?[^.]*\./g, '').trim();
          }
          
          // Reset status to present if it was late, absent, or overtime (but keep the check-in/out times)
          let newStatus = record.status;
          if (record.status === 'late' || record.status === 'overtime') {
            newStatus = 'present';
          }
          // Keep absent status as is since it's factual attendance data
          
          await updateAttendanceRecord(record.id, {
            status: newStatus,
            notes: updatedNotes || undefined
          });
        }
        
        // Reset employee statuses
        setEmployeeStatuses({});
        
        // Update last reset date
        const resetDate = new Date().toISOString();
        setLastResetDate(resetDate);
        localStorage.setItem(`payrollReset_${currentMonth}`, resetDate);
        
        alert('Monthly payroll has been reset successfully! All penalties, advances, and overtime bonuses have been cleared.');
      } catch (error) {
        console.error('Error resetting payroll:', error);
        alert('Error resetting payroll. Please try again.');
      } finally {
        setIsResetting(false);
      }
    }
  };

  const handleStatusToggle = (employeeId: string) => {
    setEmployeeStatuses(prev => ({
      ...prev,
      [employeeId]: prev[employeeId] === 'paid' ? 'pending' : 'paid'
    }));
  };

  // FIXED: Get employee payroll data with threshold-based penalties
  const getEmployeePayrollData = (employeeId: string) => {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee || !settings) return null;

    const employeeAdvances = currentMonthAdvances.filter(advance => advance.employeeId === employeeId);
    const totalEmployeeAdvances = employeeAdvances.reduce((sum, advance) => sum + advance.amount, 0);
    
    // Calculate attendance-based adjustments
    const employeeAttendance = attendance.filter(record => 
      record.employeeId === employeeId && 
      record.date.startsWith(currentMonth)
    );

    // FIXED: Calculate penalties based on thresholds
    const lateDays = employeeAttendance.filter(record => record.status === 'late').length;
    const lateDaysThreshold = settings.attendanceRules.lateDaysForPenalty || 3;
    const latePenalties = lateDays > lateDaysThreshold ? 
      (lateDays - lateDaysThreshold) * settings.attendanceRules.latePenalty : 0;

    const absentDays = employeeAttendance.filter(record => {
      if (record.status !== 'absent') return false;
      const recordDate = new Date(record.date);
      const dayOfWeek = format(recordDate, 'EEEE').toLowerCase();
      return settings.workingDays.includes(dayOfWeek);
    }).length;
    const absentDaysThreshold = settings.attendanceRules.absentDaysForPenalty || 2;
    const absencePenalties = absentDays > absentDaysThreshold ? 
      (absentDays - absentDaysThreshold) * settings.attendanceRules.absencePenalty : 0;

    // Calculate manual penalties
    const manualPenalties = employeeAttendance.reduce((sum, record) => {
      if (!record.notes) return sum;
      const penaltyMatch = record.notes.match(/Manual penalty applied: \$(\d+(?:\.\d{2})?)/);
      if (penaltyMatch) {
        return sum + parseFloat(penaltyMatch[1]);
      }
      return sum;
    }, 0);

    const totalPenalties = latePenalties + absencePenalties + manualPenalties;

    // FIXED: Calculate overtime bonuses - improved calculation
    const overtimeRecords = employeeAttendance.filter(record => 
      record.status === 'overtime' && record.checkOut
    );
    
    const overtimeBonus = overtimeRecords.reduce((sum, record) => {
      if (!record.checkOut || !settings) return sum;
      
      const [workEndHour, workEndMin] = settings.workingHours.end.split(':').map(Number);
      const [checkOutHour, checkOutMin] = record.checkOut.split(':').map(Number);
      
      const workEndMinutes = workEndHour * 60 + workEndMin;
      const checkOutMinutes = checkOutHour * 60 + checkOutMin;
      
      const overtimeMinutes = Math.max(0, checkOutMinutes - workEndMinutes);
      const overtimeHours = overtimeMinutes / 60;
      
      return sum + (overtimeHours * settings.attendanceRules.overtimeReward);
    }, 0);

    const currentSalary = Math.max(0, employee.salary - totalEmployeeAdvances - totalPenalties + overtimeBonus);

    return {
      employee,
      baseSalary: employee.salary,
      advances: totalEmployeeAdvances,
      penalties: totalPenalties,
      lateDays,
      absentDays,
      manualPenalties,
      overtimeBonus,
      overtimeHours: overtimeRecords.length,
      currentSalary,
      status: employeeStatuses[employeeId] || 'pending'
    };
  };

  // Auto-reset functionality - check if it's the 1st of the month
  useEffect(() => {
    const checkAutoReset = async () => {
      const today = new Date();
      const isFirstOfMonth = today.getDate() === 1;
      const todayString = format(today, 'yyyy-MM-dd');
      const lastResetKey = `payrollReset_${currentMonth}`;
      const hasResetThisMonth = localStorage.getItem(lastResetKey);

      // Check if we need to auto-reset (first day of month and haven't reset this month)
      if (isFirstOfMonth && !hasResetThisMonth) {
        try {
          console.log('Auto-resetting payroll for new month:', currentMonth);
          
          // Get previous month's advances to mark as paid
          const previousMonth = format(new Date(today.getFullYear(), today.getMonth() - 1, 1), 'yyyy-MM');
          const previousMonthAdvances = advances.filter(advance => 
            advance.date.startsWith(previousMonth) && advance.status === 'active'
          );

          // Mark previous month's advances as paid
          for (const advance of previousMonthAdvances) {
            await updateAdvance(advance.id, { status: 'paid' });
          }

          // Clear all attendance penalties and overtime bonuses for previous month
          const previousMonthAttendance = attendance.filter(record => 
            record.date.startsWith(previousMonth)
          );
          
          for (const record of previousMonthAttendance) {
            // Clear manual penalties from notes
            let updatedNotes = record.notes || '';
            if (updatedNotes.includes('Manual penalty applied:')) {
              updatedNotes = updatedNotes.replace(/Manual penalty applied: \$\d+(?:\.\d{2})?[^.]*\./g, '').trim();
            }
            
            // Reset status to present if it was late, absent, or overtime
            let newStatus = record.status;
            if (record.status === 'late' || record.status === 'overtime') {
              newStatus = 'present';
            }
            
            await updateAttendanceRecord(record.id, {
              status: newStatus,
              notes: updatedNotes || undefined
            });
          }

          // Mark as reset for this month
          localStorage.setItem(lastResetKey, todayString);
          setLastResetDate(todayString);
          
          console.log('Auto-reset completed for', currentMonth);
        } catch (error) {
          console.error('Error during auto-reset:', error);
        }
      }

      // Load last reset date from localStorage
      const storedResetDate = localStorage.getItem(lastResetKey);
      if (storedResetDate) {
        setLastResetDate(storedResetDate);
      }
    };

    checkAutoReset();
  }, [currentMonth, advances, updateAdvance, attendance, updateAttendanceRecord]);

  // FIXED: Get attendance penalties data with thresholds
  const getAttendancePenalties = () => {
    return activeEmployees.map(employee => {
      const employeeAttendance = attendance.filter(record => 
        record.employeeId === employee.id && 
        record.date.startsWith(currentMonth)
      );

      const lateDays = employeeAttendance.filter(record => record.status === 'late').length;
      const absentDays = employeeAttendance.filter(record => {
        if (record.status !== 'absent') return false;
        const recordDate = new Date(record.date);
        const dayOfWeek = format(recordDate, 'EEEE').toLowerCase();
        return settings?.workingDays.includes(dayOfWeek);
      }).length;

      const manualPenalties = employeeAttendance.reduce((sum, record) => {
        if (!record.notes) return sum;
        const penaltyMatch = record.notes.match(/Manual penalty applied: \$(\d+(?:\.\d{2})?)/);
        if (penaltyMatch) {
          return sum + parseFloat(penaltyMatch[1]);
        }
        return sum;
      }, 0);

      // FIXED: Apply penalties only after thresholds
      const lateDaysThreshold = settings?.attendanceRules.lateDaysForPenalty || 3;
      const absentDaysThreshold = settings?.attendanceRules.absentDaysForPenalty || 2;

      const latePenalties = settings && lateDays > lateDaysThreshold ? 
        (lateDays - lateDaysThreshold) * settings.attendanceRules.latePenalty : 0;
      
      const absencePenalties = settings && absentDays > absentDaysThreshold ? 
        (absentDays - absentDaysThreshold) * settings.attendanceRules.absencePenalty : 0;

      return {
        employee,
        lateDays,
        absentDays,
        latePenalties,
        absencePenalties,
        manualPenalties,
        totalPenalties: latePenalties + absencePenalties + manualPenalties,
        lateDaysThreshold,
        absentDaysThreshold
      };
    }).filter(data => data.totalPenalties > 0);
  };

  // FIXED: Get overtime bonuses data - improved calculation
  const getOvertimeBonuses = () => {
    return activeEmployees.map(employee => {
      const employeeAttendance = attendance.filter(record => 
        record.employeeId === employee.id && 
        record.date.startsWith(currentMonth) &&
        record.status === 'overtime' &&
        record.checkOut
      );

      const overtimeBonus = employeeAttendance.reduce((sum, record) => {
        if (!record.checkOut || !settings) return sum;
        
        const [workEndHour, workEndMin] = settings.workingHours.end.split(':').map(Number);
        const [checkOutHour, checkOutMin] = record.checkOut.split(':').map(Number);
        
        const workEndMinutes = workEndHour * 60 + workEndMin;
        const checkOutMinutes = checkOutHour * 60 + checkOutMin;
        
        const overtimeMinutes = Math.max(0, checkOutMinutes - workEndMinutes);
        const overtimeHours = overtimeMinutes / 60;
        
        return sum + (overtimeHours * settings.attendanceRules.overtimeReward);
      }, 0);

      return {
        employee,
        overtimeSessions: employeeAttendance.length,
        overtimeBonus,
        records: employeeAttendance
      };
    }).filter(data => data.overtimeBonus > 0);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Payroll Management</h1>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Period: {format(new Date(currentMonth), 'MMMM yyyy')}
            {lastResetDate && (
              <span className="ml-2 text-green-600 dark:text-green-400">
                • Last reset: {format(new Date(lastResetDate), 'MMM d, yyyy')}
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleGeneratePayroll}
            disabled={isGenerating}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            <Download className={`h-4 w-4 ${isGenerating ? 'animate-spin' : ''}`} />
            <span>{isGenerating ? 'Generating...' : 'Export Report'}</span>
          </button>
          <button
            onClick={handleResetPayroll}
            disabled={isResetting}
            className="flex items-center space-x-2 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors"
          >
            <RefreshCw className={`h-4 w-4 ${isResetting ? 'animate-spin' : ''}`} />
            <span>{isResetting ? 'Resetting...' : 'Reset Monthly Payroll'}</span>
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">${totalCurrentPayroll.toLocaleString()}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Total Payroll</p>
              <p className="text-xs text-gray-500">After adjustments</p>
            </div>
            <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-red-600">-${totalAdvances}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Total Advances</p>
              <p className="text-xs text-gray-500">Deducted from salaries</p>
            </div>
            <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
              <TrendingDown className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-red-600">-${totalPenalties.toFixed(0)}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Total Penalties</p>
              <p className="text-xs text-gray-500">After threshold days</p>
            </div>
            <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
              <AlertCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-purple-600">+${totalOvertimeBonuses.toFixed(0)}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Overtime Bonuses</p>
              <p className="text-xs text-gray-500">Extra hours worked</p>
            </div>
            <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <Zap className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{activeEmployees.length}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Active Employees</p>
              <p className="text-xs text-gray-500">Receiving salary</p>
            </div>
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Payroll Information */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Smart Payroll System</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Payroll automatically adjusts based on attendance rules and working days. Penalties only apply after threshold days are reached. Auto-resets on the 1st of each month.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div>
            <h3 className="font-medium text-gray-900 dark:text-white mb-4">Automatic Deductions</h3>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <li>• Late arrivals: ${settings?.attendanceRules.latePenalty || 0}/day (after {settings?.attendanceRules.lateDaysForPenalty || 3} days)</li>
              <li>• Absences on working days: ${settings?.attendanceRules.absencePenalty || 0}/day (after {settings?.attendanceRules.absentDaysForPenalty || 2} days)</li>
              <li>• Employee advances: Deducted monthly</li>
              <li>• Manual penalties: Applied via attendance system</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 dark:text-white mb-4">Automatic Bonuses</h3>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <li>• Overtime work: ${settings?.attendanceRules.overtimeReward || 0}/hour</li>
              <li>• Working beyond {settings?.workingHours.end || '18:00'}</li>
              <li>• Calculated automatically from check-out times</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 dark:text-white mb-4">Reset Features</h3>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <li>• Resets automatically on 1st of each month</li>
              <li>• Manual reset clears all penalties and bonuses</li>
              <li>• Advances marked as processed</li>
              <li>• Final salary returns to base salary</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Employee Payroll Details */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Employee Payroll Details</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">Smart payroll with threshold-based penalties and overtime bonuses</p>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Employee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Base Salary
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Advances
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Penalties
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Overtime Bonus
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Final Salary
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {activeEmployees.map((employee) => {
                const payrollData = getEmployeePayrollData(employee.id);
                if (!payrollData) return null;

                return (
                  <tr key={employee.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                            <span className="text-blue-600 dark:text-blue-400 font-medium text-xs">
                              {employee.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {employee.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {employee.position}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      ${payrollData.baseSalary.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 dark:text-red-400">
                      {payrollData.advances > 0 ? `-$${payrollData.advances}` : '$0'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 dark:text-red-400">
                      {payrollData.penalties > 0 ? (
                        <div>
                          <div>-${payrollData.penalties.toFixed(0)}</div>
                          <div className="text-xs text-gray-500">
                            {payrollData.lateDays}L, {payrollData.absentDays}A
                            {payrollData.manualPenalties > 0 && `, $${payrollData.manualPenalties}M`}
                          </div>
                        </div>
                      ) : '$0'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-purple-600 dark:text-purple-400">
                      {payrollData.overtimeBonus > 0 ? (
                        <div>
                          <div>+${payrollData.overtimeBonus.toFixed(0)}</div>
                          <div className="text-xs text-gray-500">
                            {payrollData.overtimeHours} sessions
                          </div>
                        </div>
                      ) : '$0'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      ${payrollData.currentSalary.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleStatusToggle(employee.id)}
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full transition-colors ${
                          payrollData.status === 'paid'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-800'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 hover:bg-yellow-200 dark:hover:bg-yellow-800'
                        }`}
                      >
                        {payrollData.status === 'paid' ? 'Paid' : 'Pending'}
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Recent Payroll Adjustments */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Payroll Adjustments</h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">Latest attendance-based adjustments affecting current payroll (penalties apply after threshold days)</p>

        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8">
            <button 
              onClick={() => setActiveTab('advances')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'advances'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400'
              }`}
            >
              Recent Advances
            </button>
            <button 
              onClick={() => setActiveTab('penalties')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'penalties'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400'
              }`}
            >
              Attendance Penalties
            </button>
            <button 
              onClick={() => setActiveTab('overtime')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overtime'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400'
              }`}
            >
              Overtime Bonuses
            </button>
          </nav>
        </div>

        <div className="mt-6">
          {activeTab === 'advances' && (
            <div>
              {currentMonthAdvances.length > 0 ? (
                <div className="space-y-4">
                  {currentMonthAdvances.map((advance) => {
                    const employee = employees.find(emp => emp.id === advance.employeeId);
                    return (
                      <div key={advance.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <DollarSign className="h-4 w-4 text-red-600" />
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{employee?.name}</p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">{advance.reason}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-red-600 dark:text-red-400">-${advance.amount}</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{format(new Date(advance.date), 'M/d/yyyy')}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 text-center py-8">No recent advances found</p>
              )}
            </div>
          )}

          {activeTab === 'penalties' && (
            <div>
              {getAttendancePenalties().length > 0 ? (
                <div className="space-y-4">
                  {getAttendancePenalties().map((data) => (
                    <div key={data.employee.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <AlertCircle className="h-4 w-4 text-red-600" />
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">{data.employee.name}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {data.lateDays} late days (threshold: {data.lateDaysThreshold}), {data.absentDays} absent days (threshold: {data.absentDaysThreshold})
                            {data.manualPenalties > 0 && `, $${data.manualPenalties} manual penalties`}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-red-600 dark:text-red-400">-${data.totalPenalties.toFixed(0)}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Total penalties</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 text-center py-8">No attendance penalties this month (thresholds not reached)</p>
              )}
            </div>
          )}

          {activeTab === 'overtime' && (
            <div>
              {getOvertimeBonuses().length > 0 ? (
                <div className="space-y-4">
                  {getOvertimeBonuses().map((data) => (
                    <div key={data.employee.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Zap className="h-4 w-4 text-purple-600" />
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">{data.employee.name}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {data.overtimeSessions} overtime sessions this month
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-purple-600 dark:text-purple-400">+${data.overtimeBonus.toFixed(0)}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Overtime bonus</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 text-center py-8">No overtime bonuses this month</p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}