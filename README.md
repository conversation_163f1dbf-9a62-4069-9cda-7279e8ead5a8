# SalonSys - Desktop Salon Management System

A comprehensive salon management system built with React, TypeScript, and Electron, featuring local SQLite database storage for reliable data persistence.

## Features

- **Employee Management**: Track staff information, attendance, and payroll
- **Customer Management**: Maintain customer profiles and booking history
- **Service Management**: Manage services, packages, and dress rentals
- **Booking System**: Schedule appointments with calendar integration
- **Financial Dashboard**: Track revenue, expenses, and financial reports
- **Attendance Tracking**: Monitor employee attendance and overtime
- **Payroll Management**: Calculate salaries, advances, and deductions
- **Multi-language Support**: English and Arabic language support
- **Dark/Light Theme**: Customizable UI themes
- **Data Backup/Restore**: Secure data backup and restore functionality

## Technology Stack

- **Frontend**: React 18, TypeScript, TailwindCSS
- **Desktop Framework**: Electron
- **Database**: SQLite (via better-sqlite3)
- **Build Tool**: Vite
- **Icons**: Lucide React

## Installation

### Prerequisites

- Node.js 18 or higher
- npm or yarn package manager

### Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd SalonSys
```

2. Install dependencies:
```bash
npm install
```

3. Build the application:
```bash
npm run build
```

4. Run the desktop application:
```bash
npm run electron
```

## Development

### Development Mode

To run the application in development mode:

```bash
# Start the development server and Electron app
npm run electron:dev
```

This will start the Vite development server and launch the Electron app with hot reload.

### Building

```bash
# Build both renderer and main processes
npm run build

# Build only the renderer (React app)
npm run build:renderer

# Build only the main process (Electron)
npm run build:electron
```

### Packaging

To create distributable packages:

```bash
# Create packages for current platform
npm run electron:pack

# Create distribution packages
npm run electron:dist
```

## Database

The application uses SQLite for local data storage, providing:

- **Reliable persistence**: Data is stored locally in a SQLite database
- **Performance**: Fast queries and transactions
- **Portability**: Database file can be easily backed up and restored
- **ACID compliance**: Ensures data integrity

### Database Location

The SQLite database is stored in the application's user data directory:
- **Windows**: `%APPDATA%/SalonSys/salonsys.db`
- **macOS**: `~/Library/Application Support/SalonSys/salonsys.db`
- **Linux**: `~/.config/SalonSys/salonsys.db`

### Data Migration

If you're upgrading from a web version that used IndexedDB, the application includes an automatic migration system that will:

1. Detect existing IndexedDB data
2. Export the data from IndexedDB
3. Import it into the new SQLite database
4. Preserve all your existing data

## Backup and Restore

The application includes built-in backup and restore functionality:

### Creating Backups

1. Go to Settings → Backup & Data
2. Click "Create Backup"
3. Choose a location to save your backup file
4. The backup will be saved as a JSON file with timestamp

### Restoring from Backup

1. Go to Settings → Backup & Data
2. Click "Restore from Backup"
3. Select your backup file
4. Confirm the restoration (this will replace all current data)
5. The application will reload with restored data

### Backup Best Practices

- Create regular backups before making major changes
- Store backups in multiple locations (cloud storage, external drives)
- Test your backups periodically by restoring to a test environment
- Keep multiple backup versions for different time periods

## Configuration

### Settings

The application provides comprehensive settings for:

- **Working Days & Hours**: Configure business hours and working days
- **Holidays & Day Offs**: Manage company holidays and employee time off
- **Attendance Rules**: Set penalties, rewards, and allowance times
- **Layout**: Customize theme, language, and UI preferences
- **Backup & Data**: Manage data backups and view application information

### Customization

- **Themes**: Switch between light and dark modes
- **Languages**: Support for English and Arabic
- **Colors**: Multiple color schemes available
- **RTL Support**: Automatic right-to-left layout for Arabic

## Security

The application implements several security measures:

- **Local Data Storage**: All data is stored locally, no cloud dependencies
- **Content Security Policy**: Prevents XSS attacks
- **Secure IPC**: Safe communication between main and renderer processes
- **No Remote Code Execution**: All code is bundled and verified

## Troubleshooting

### Common Issues

1. **Application won't start**:
   - Ensure Node.js 18+ is installed
   - Run `npm install` to install dependencies
   - Check for any error messages in the console

2. **Database errors**:
   - Check if the user data directory is writable
   - Try creating a backup and restoring to reset the database
   - Contact support if issues persist

3. **Migration issues**:
   - Ensure you have a backup before migration
   - Try the manual export/import process if automatic migration fails

### Getting Help

If you encounter issues:

1. Check the console for error messages
2. Try restarting the application
3. Create a backup of your data
4. Contact technical support with error details

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For technical support or feature requests, please contact the development team.
