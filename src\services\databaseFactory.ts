// Database Factory - chooses between IndexedDB and SQLite based on environment

import { database as indexedDatabase } from './database';
import { sqliteDatabase } from './sqliteDatabase';

// Common interface for both database implementations
export interface DatabaseInterface {
  init(): Promise<void>;
  getAll<T>(storeName: string): Promise<T[]>;
  get<T>(storeName: string, id: string): Promise<T | undefined>;
  add<T>(storeName: string, data: T): Promise<void>;
  update<T>(storeName: string, data: T): Promise<void>;
  delete(storeName: string, id: string): Promise<void>;
  getByIndex<T>(storeName: string, indexName: string, value: any): Promise<T[]>;
  clearAllData(): Promise<void>;
}

class DatabaseFactory {
  private static instance: DatabaseInterface | null = null;

  static getDatabase(): DatabaseInterface {
    if (this.instance) {
      return this.instance;
    }

    // Check if we're in Electron environment
    const isElectron = typeof window !== 'undefined' && window.electronAPI;
    
    if (isElectron) {
      console.log('Using SQLite database (Electron environment)');
      this.instance = sqliteDatabase;
    } else {
      console.log('Using IndexedDB database (Web environment)');
      this.instance = indexedDatabase;
    }

    return this.instance;
  }

  // Method to force a specific database type (useful for testing)
  static setDatabase(database: DatabaseInterface): void {
    this.instance = database;
  }

  // Reset the factory (useful for testing)
  static reset(): void {
    this.instance = null;
  }
}

// Export the database instance
export const database = DatabaseFactory.getDatabase();
