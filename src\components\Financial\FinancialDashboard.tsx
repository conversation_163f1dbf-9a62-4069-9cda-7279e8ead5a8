import React, { useState } from 'react';
import { DollarSign, TrendingUp, BarChart3, RefreshCw, Plus } from 'lucide-react';
import { useBookings, useServices, useServicePackages, useDressRentals, useSettings } from '../../hooks/useDatabase';
import { useTranslation } from '../../utils/translations';
import { format, startOfMonth, endOfMonth, startOfYear, endOfYear, eachMonthOfInterval, subMonths } from 'date-fns';

export default function FinancialDashboard() {
  const { bookings } = useBookings();
  const { services } = useServices();
  const { packages } = useServicePackages();
  const { dresses } = useDressRentals();
  const { settings } = useSettings(true);
  const t = useTranslation(settings?.language || 'en');
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');

  // Calculate revenue from completed bookings only (exclude cancelled)
  const completedBookings = bookings.filter(booking => booking.status === 'completed');
  const totalRevenue = completedBookings.reduce((sum, booking) => sum + booking.totalAmount, 0);

  // Calculate individual services revenue from completed bookings
  const servicesRevenue = completedBookings.reduce((sum, booking) => {
    const serviceAmount = booking.serviceIds.reduce((serviceSum, serviceId) => {
      const service = services.find(s => s.id === serviceId);
      return serviceSum + (service ? service.price : 0);
    }, 0);
    return sum + serviceAmount;
  }, 0);

  // Calculate packages revenue from completed bookings
  const packagesRevenue = completedBookings.reduce((sum, booking) => {
    const packageAmount = booking.packageIds.reduce((packageSum, packageId) => {
      const pkg = packages.find(p => p.id === packageId);
      return packageSum + (pkg ? pkg.discountedPrice : 0);
    }, 0);
    return sum + packageAmount;
  }, 0);

  // Calculate dress rentals revenue from completed bookings
  const dressRentalsRevenue = completedBookings.reduce((sum, booking) => {
    const dressAmount = booking.dressIds.reduce((dressSum, dressId) => {
      const dress = dresses.find(d => d.id === dressId);
      return dressSum + (dress ? dress.rentalPrice : 0);
    }, 0);
    return sum + dressAmount;
  }, 0);

  // Calculate top performing employees based on completed bookings
  const employeePerformance = completedBookings.reduce((acc, booking) => {
    booking.employeeIds.forEach(employeeId => {
      if (!acc[employeeId]) {
        acc[employeeId] = { services: 0, revenue: 0 };
      }
      acc[employeeId].services += 1;
      acc[employeeId].revenue += booking.totalAmount / booking.employeeIds.length; // Split revenue among employees
    });
    return acc;
  }, {} as Record<string, { services: number; revenue: number }>);

  const topEmployees = Object.entries(employeePerformance)
    .map(([employeeId, data]) => ({
      name: employeeId === 'emp1' ? 'Emma Johnson' : 'David Smith', // Mock names for demo
      services: data.services,
      revenue: data.revenue
    }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

  const revenueCategories = [
    { name: t.services, amount: servicesRevenue, color: 'bg-blue-600' },
    { name: t.packages, amount: packagesRevenue, color: 'bg-green-600' },
    { name: t.dressRentals, amount: dressRentalsRevenue, color: 'bg-purple-600' },
  ];

  const maxAmount = Math.max(...revenueCategories.map(cat => cat.amount), 1);

  // Generate monthly revenue data for chart
  const getMonthlyRevenueData = () => {
    const now = new Date();
    const months = eachMonthOfInterval({
      start: subMonths(now, 5),
      end: now
    });

    return months.map(month => {
      const monthStart = startOfMonth(month);
      const monthEnd = endOfMonth(month);
      
      const monthlyBookings = completedBookings.filter(booking => {
        const bookingDate = new Date(booking.date);
        return bookingDate >= monthStart && bookingDate <= monthEnd;
      });

      const monthlyRevenue = monthlyBookings.reduce((sum, booking) => sum + booking.totalAmount, 0);
      
      return {
        month: format(month, 'MMM'),
        revenue: monthlyRevenue
      };
    });
  };

  const monthlyData = getMonthlyRevenueData();
  const maxMonthlyRevenue = Math.max(...monthlyData.map(d => d.revenue), 1);
  const isRTL = settings?.language === 'ar' || settings?.rtlMode;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t.financialDashboard}</h1>
        </div>
        <div className={`flex items-center space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className={`px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
          >
            <option value="monthly">{t.monthly}</option>
            <option value="weekly">{t.weekly}</option>
            <option value="yearly">{t.yearly}</option>
          </select>
          <button className={`flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
            <RefreshCw className="h-4 w-4" />
            <span>{t.refresh}</span>
          </button>
        </div>
      </div>

      {/* Revenue Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">${totalRevenue.toFixed(2)}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{t.totalRevenue}</p>
              <p className="text-xs text-gray-500">{t.fromCompletedBookingsOnly}</p>
            </div>
            <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">${servicesRevenue.toFixed(2)}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{t.servicesRevenue}</p>
              <p className="text-xs text-gray-500">{t.individualServicesOnly}</p>
            </div>
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Plus className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">${packagesRevenue.toFixed(2)}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{t.packagesRevenue}</p>
              <p className="text-xs text-gray-500">{t.servicePackages}</p>
            </div>
            <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <Plus className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">${dressRentalsRevenue.toFixed(2)}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{t.dressRentalsRevenue}</p>
              <p className="text-xs text-gray-500">{t.dressRentalIncome}</p>
            </div>
            <div className="p-2 bg-pink-100 dark:bg-pink-900 rounded-lg">
              <Plus className="h-6 w-6 text-pink-600 dark:text-pink-400" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue by Category */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-6">
            <h2 className={`text-lg font-semibold text-gray-900 dark:text-white mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>{t.revenueByCategory}</h2>
            <p className={`text-sm text-gray-600 dark:text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>{t.breakdownOfRevenueSources}</p>
          </div>

          <div className="space-y-4">
            {revenueCategories.map((category, index) => (
              <div key={index} className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`flex items-center space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  <div className={`w-3 h-3 rounded-full ${category.color}`}></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">{category.name}</span>
                </div>
                <span className="text-sm font-bold text-gray-900 dark:text-white">${category.amount.toFixed(2)}</span>
              </div>
            ))}
          </div>

          <div className="mt-6 space-y-3">
            {revenueCategories.map((category, index) => (
              <div key={index}>
                <div className={`flex justify-between text-sm mb-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-gray-600 dark:text-gray-400">{category.name}</span>
                  <span className="text-gray-900 dark:text-white">${category.amount.toFixed(2)}</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${category.color}`}
                    style={{ width: `${maxAmount > 0 ? (category.amount / maxAmount) * 100 : 0}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Performing Employees */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-6">
            <h2 className={`text-lg font-semibold text-gray-900 dark:text-white mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>{t.topPerformingEmployees}</h2>
            <p className={`text-sm text-gray-600 dark:text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>{t.basedOnRevenueFromCompletedBookings}</p>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className={`text-sm font-medium text-gray-500 dark:text-gray-400 pb-2 ${isRTL ? 'text-right' : 'text-left'}`}>{t.employee}</th>
                  <th className={`text-sm font-medium text-gray-500 dark:text-gray-400 pb-2 ${isRTL ? 'text-right' : 'text-left'}`}>{t.services}</th>
                  <th className={`text-sm font-medium text-gray-500 dark:text-gray-400 pb-2 ${isRTL ? 'text-right' : 'text-left'}`}>{t.totalRevenue}</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {topEmployees.length > 0 ? topEmployees.map((employee, index) => (
                  <tr key={index}>
                    <td className={`py-3 text-sm font-medium text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}>{employee.name}</td>
                    <td className={`py-3 text-sm text-gray-600 dark:text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>{employee.services}</td>
                    <td className={`py-3 text-sm font-medium text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}>${employee.revenue.toFixed(2)}</td>
                  </tr>
                )) : (
                  <tr>
                    <td colSpan={3} className="py-8 text-center text-gray-500 dark:text-gray-400">
                      {t.noPerformanceDataAvailable}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Monthly Revenue Trend */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="mb-6">
          <h2 className={`text-lg font-semibold text-gray-900 dark:text-white mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>{t.monthlyRevenueTrend}</h2>
          <p className={`text-sm text-gray-600 dark:text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>
            {t.revenueOverTimeFromCompletedBookings}
          </p>
        </div>

        {/* Chart */}
        <div className="h-64 flex items-end justify-between space-x-2">
          {monthlyData.map((data, index) => {
            const height = maxMonthlyRevenue > 0 ? (data.revenue / maxMonthlyRevenue) * 100 : 0;
            const isCurrentMonth = index === monthlyData.length - 1;
            
            return (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="w-full flex flex-col items-center">
                  <span className="text-xs text-gray-600 dark:text-gray-400 mb-1">${data.revenue.toFixed(0)}</span>
                  <div 
                    className={`w-full rounded-t-lg transition-all duration-300 hover:bg-blue-700 ${
                      isCurrentMonth ? 'bg-blue-600' : 'bg-gray-400 dark:bg-gray-600'
                    }`}
                    style={{ height: `${Math.max(height, 5)}%` }}
                  ></div>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400 mt-2">{data.month}</span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}