import React from 'react';
import { Bell, Search, Menu } from 'lucide-react';

interface HeaderProps {
  darkMode: boolean;
  onToggleDarkMode: () => void;
  sidebarCollapsed: boolean;
  rtlMode: boolean;
}

export default function Header({ darkMode, onToggleDarkMode, sidebarCollapsed, rtlMode }: HeaderProps) {
  return (
    <header className={`
      bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 sm:px-6 py-4 transition-all duration-300
      ${rtlMode 
        ? (sidebarCollapsed ? 'lg:mr-16' : 'lg:mr-64')
        : (sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64')
      }
    `}>
      <div className={`flex items-center justify-between ${rtlMode ? 'flex-row-reverse' : ''}`}>
        {/* Search Section */}
        <div className={`flex items-center ${rtlMode ? 'flex-row-reverse space-x-reverse' : ''} space-x-4`}>
          <div className="relative">
            <Search className={`absolute top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 ${rtlMode ? 'right-3' : 'left-3'}`} />
            <input
              type="text"
              placeholder="Search..."
              className={`
                ${rtlMode ? 'pr-10 pl-4 text-right' : 'pl-10 pr-4 text-left'} 
                py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
                focus:ring-2 focus:ring-primary-500 focus:border-transparent 
                bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                w-48 sm:w-64 md:w-80
              `}
            />
          </div>
        </div>

        {/* User Section */}
        <div className={`flex items-center ${rtlMode ? 'flex-row-reverse space-x-reverse' : ''} space-x-4`}>
          {/* Notifications */}
          <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors relative">
            <Bell className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            <span className={`absolute top-0 h-2 w-2 bg-red-500 rounded-full ${rtlMode ? 'left-0' : 'right-0'}`}></span>
          </button>

          {/* User Profile */}
          <div className={`flex items-center ${rtlMode ? 'flex-row-reverse space-x-reverse' : ''} space-x-3`}>
            <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">A</span>
            </div>
            <div className={`hidden sm:block ${rtlMode ? 'text-right' : 'text-left'}`}>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Admin User</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">Administrator</p>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}