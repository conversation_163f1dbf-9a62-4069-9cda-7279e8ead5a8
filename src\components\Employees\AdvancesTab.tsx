import React, { useState } from 'react';
import { DollarSign, Plus, Calendar, User } from 'lucide-react';
import { useEmployees, useAdvances, useSettings } from '../../hooks/useDatabase';
import { format } from 'date-fns';
import { useTranslation } from '../../utils/translations';
import RequestAdvanceModal from './RequestAdvanceModal';

export default function AdvancesTab() {
  const { employees } = useEmployees();
  const { advances } = useAdvances();
  const { settings } = useSettings(true);
  const t = useTranslation(settings?.language || 'en');
  const [showRequestModal, setShowRequestModal] = useState(false);

  const activeEmployees = employees.filter(emp => emp.status === 'active');
  
  const getEmployeeAdvances = (employeeId: string) => {
    return advances.filter(advance => advance.employeeId === employeeId && advance.status === 'active');
  };

  const getTotalAdvanceAmount = (employeeId: string) => {
    return getEmployeeAdvances(employeeId).reduce((sum, advance) => sum + advance.amount, 0);
  };

  const getCurrentMonthAdvances = () => {
    const currentMonth = format(new Date(), 'yyyy-MM');
    return advances.filter(advance => advance.date.startsWith(currentMonth));
  };

  const currentMonthAdvances = getCurrentMonthAdvances();
  const isRTL = settings?.language === 'ar' || settings?.rtlMode;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t.teamManagement}</h1>
        </div>
        <button
          onClick={() => setShowRequestModal(true)}
          className={`flex items-center space-x-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
        >
          <DollarSign className="h-4 w-4" />
          <span>{t.requestAdvance}</span>
        </button>
      </div>

      {/* Advances Section */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t.employeeAdvances}</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">{t.manageEmployeeSalaryAdvances}</p>
            </div>
          </div>
        </div>

        {/* Advances Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.employee}
                </th>
                <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.amount}
                </th>
                <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.reason}
                </th>
                <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.date}
                </th>
                <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.status}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {advances.map((advance) => {
                const employee = employees.find(emp => emp.id === advance.employeeId);
                if (!employee) return null;

                return (
                  <tr key={advance.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse space-x-reverse' : ''} space-x-4`}>
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                            <span className="text-blue-600 dark:text-blue-400 font-medium text-xs">
                              {employee.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                        </div>
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {employee.name}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap ${isRTL ? 'text-right' : 'text-left'}`}>
                      <span className="text-sm font-medium text-red-600 dark:text-red-400">
                        -${advance.amount}
                      </span>
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}>
                      {advance.reason}
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {format(new Date(advance.date), 'M/d/yyyy')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        advance.status === 'active'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                          : advance.status === 'paid'
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                      }`}>
                        {advance.status === 'active' ? t.active : advance.status === 'paid' ? t.paid : t.cancelled}
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {advances.length === 0 && (
          <div className="text-center py-12">
            <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">{t.noAdvancesFound}</p>
          </div>
        )}
      </div>

      {/* Current Month Summary */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h2 className={`text-lg font-semibold text-gray-900 dark:text-white mb-4 ${isRTL ? 'text-right' : 'text-left'}`}>{t.currentMonthAdvancesSummary}</h2>
        <p className={`text-sm text-gray-600 dark:text-gray-400 mb-6 ${isRTL ? 'text-right' : 'text-left'}`}>
          {format(new Date(), 'MMMM yyyy')} {t.advancesOverview}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {activeEmployees.map((employee) => {
            const employeeAdvances = getEmployeeAdvances(employee.id);
            const totalAdvances = getTotalAdvanceAmount(employee.id);
            const advanceCount = employeeAdvances.length;

            return (
              <div key={employee.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className={`flex items-center space-x-2 mb-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 dark:text-blue-400 font-medium text-xs">
                      {employee.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <span className="font-medium text-gray-900 dark:text-white">{employee.name}</span>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className="text-gray-600 dark:text-gray-400">{t.baseSalary}:</span>
                    <span className="font-medium text-gray-900 dark:text-white">${employee.salary.toLocaleString()}</span>
                  </div>
                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className="text-gray-600 dark:text-gray-400">{t.advances}:</span>
                    <span className="font-medium text-red-600 dark:text-red-400">-${totalAdvances}</span>
                  </div>
                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className="text-gray-600 dark:text-gray-400">{t.count}:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{advanceCount} {t.advancesCount}</span>
                  </div>
                </div>
                
                <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-red-600 h-2 rounded-full" 
                      style={{ width: `${Math.min((totalAdvances / employee.salary) * 100, 100)}%` }}
                    ></div>
                  </div>
                  <p className={`text-xs text-gray-500 dark:text-gray-400 mt-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {((totalAdvances / employee.salary) * 100).toFixed(1)}% {t.ofSalary}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Request Advance Modal */}
      <RequestAdvanceModal
        isOpen={showRequestModal}
        onClose={() => setShowRequestModal(false)}
        employees={activeEmployees}
      />
    </div>
  );
}