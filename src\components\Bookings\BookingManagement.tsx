import React, { useState, useEffect } from 'react';
import { Calendar as CalendarIcon, Plus, ChevronLeft, ChevronRight, Clock, User, Edit, X, CheckCircle } from 'lucide-react';
import { format, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, parseISO } from 'date-fns';
import { useBookings, useSettings } from '../../hooks/useDatabase';
import { useTranslation } from '../../utils/translations';
import BookingForm from './BookingForm';

export default function BookingManagement() {
  const { bookings, updateBooking } = useBookings();
  const { settings } = useSettings(true);
  const t = useTranslation(settings?.language || 'en');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [showBookingForm, setShowBookingForm] = useState(false);
  const [editingBooking, setEditingBooking] = useState(null);

  const selectedDateString = format(selectedDate, 'yyyy-MM-dd');
  const todayBookings = bookings.filter(booking => booking.date === selectedDateString);

  // Calendar generation
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const calendarDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  const daysOfWeek = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  // Format time to 12-hour format with AM/PM
  const formatTime = (time: string) => {
    if (!time) return '';
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const handleEditBooking = (booking: any) => {
    setEditingBooking(booking);
    setShowBookingForm(true);
  };

  const handleNewBooking = () => {
    setEditingBooking(null);
    setShowBookingForm(true);
  };

  const handleCancelBooking = async (bookingId: string) => {
    if (confirm('Are you sure you want to cancel this booking?')) {
      await updateBooking(bookingId, { status: 'cancelled' });
    }
  };

  const handleMarkCompleted = async (bookingId: string) => {
    await updateBooking(bookingId, { status: 'completed' });
  };

  const isRTL = settings?.language === 'ar' || settings?.rtlMode;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t.bookingManagement}</h1>
        </div>
        <button
          onClick={handleNewBooking}
          className={`flex items-center space-x-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
        >
          <Plus className="h-4 w-4" />
          <span>{t.newBooking}</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Calendar Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-4">
            <h2 className={`text-lg font-semibold text-gray-900 dark:text-white mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>{t.calendar}</h2>
            <p className={`text-sm text-gray-600 dark:text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>{t.selectDateToViewBookings}</p>
          </div>

          {/* Calendar Header */}
          <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              onClick={() => setCurrentMonth(subMonths(currentMonth, 1))}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <h3 className="font-semibold text-gray-900 dark:text-white">
              {format(currentMonth, 'MMMM yyyy')}
            </h3>
            <button
              onClick={() => setCurrentMonth(addMonths(currentMonth, 1))}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {daysOfWeek.map(day => (
              <div key={day} className="text-center text-xs font-medium text-gray-500 p-2">
                {day}
              </div>
            ))}
          </div>

          <div className="grid grid-cols-7 gap-1">
            {calendarDays.map(day => {
              const isSelected = isSameDay(day, selectedDate);
              const isToday = isSameDay(day, new Date());
              const dayBookings = bookings.filter(booking => booking.date === format(day, 'yyyy-MM-dd'));
              const hasBookings = dayBookings.length > 0;
              
              return (
                <button
                  key={day.toISOString()}
                  onClick={() => setSelectedDate(day)}
                  className={`
                    p-2 text-sm rounded-lg transition-colors relative
                    ${isSelected 
                      ? 'bg-gray-900 text-white' 
                      : isToday 
                        ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                    }
                  `}
                >
                  {format(day, 'd')}
                  {hasBookings && (
                    <div className={`absolute bottom-1 transform w-1 h-1 bg-blue-600 rounded-full ${isRTL ? 'right-1/2 translate-x-1/2' : 'left-1/2 -translate-x-1/2'}`}></div>
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Bookings for Selected Date */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {t.bookingsFor} {format(selectedDate, 'M/d/yyyy')}
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {t.manageAppointmentsAndServices} ({todayBookings.length} {t.bookings})
                </p>
              </div>
            </div>
          </div>

          <div className="p-6">
            {todayBookings.length > 0 ? (
              <div className="space-y-4">
                {todayBookings.map((booking) => (
                  <div key={booking.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                        <div className={`flex items-center space-x-2 mb-2 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                          <Clock className="h-4 w-4 text-gray-500" />
                          <span className="font-medium text-gray-900 dark:text-white">
                            {formatTime(booking.startTime)} - {formatTime(booking.endTime)}
                          </span>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(booking.status)}`}>
                            {booking.status === 'confirmed' ? t.confirmed :
                             booking.status === 'completed' ? t.completed :
                             booking.status === 'cancelled' ? t.cancelled : t.pending}
                          </span>
                        </div>
                        
                        <div className="mb-2">
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {booking.title || `${booking.customerName} Appointment`}
                          </h4>
                          <div className={`flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                            <User className="h-3 w-3" />
                            <span>Staff: Emma Johnson</span>
                            <span className="mx-2">•</span>
                            <span>Amount: ${booking.totalAmount}</span>
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {booking.notes || `${t.regularCustomer}, ${t.prefersNaturalLook}`}
                        </p>
                      </div>
                      
                      <div className={`flex flex-col items-end space-y-2 ml-4 ${isRTL ? 'mr-4 ml-0 items-start' : ''}`}>
                        <div className={`flex items-center space-x-2 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                          <button
                            onClick={() => handleEditBooking(booking)}
                            className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                          >
                            {t.edit}
                          </button>
                          {booking.status !== 'cancelled' && booking.status !== 'completed' && (
                            <button
                              onClick={() => handleCancelBooking(booking.id)}
                              className="px-3 py-1 text-xs bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
                            >
                              {t.cancel}
                            </button>
                          )}
                        </div>
                        
                        {booking.status === 'confirmed' && (
                          <button
                            onClick={() => handleMarkCompleted(booking.id)}
                            className={`px-3 py-1 text-xs bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded hover:bg-green-200 dark:hover:bg-green-800 transition-colors flex items-center space-x-1 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
                          >
                            <CheckCircle className="h-3 w-3" />
                            <span>{t.markCompleted}</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <CalendarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400 mb-4">{t.noBookingsForThisDate}</p>
                <button
                  onClick={handleNewBooking}
                  className={`inline-flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
                >
                  <Plus className="h-4 w-4" />
                  <span>{t.createNewBooking}</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Booking Form Modal */}
      {showBookingForm && (
        <BookingForm
          booking={editingBooking}
          selectedDate={selectedDate}
          onClose={() => {
            setShowBookingForm(false);
            setEditingBooking(null);
          }}
        />
      )}
    </div>
  );
}