const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const Database = require('better-sqlite3');
const fs = require('fs');
const os = require('os');

const __dirname = path.dirname(__filename);

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.cjs
// │
process.env.APP_ROOT = path.join(__dirname, '..');

const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL'];
const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron');
const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist');

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST;

// Database setup
const isDev = process.env.NODE_ENV === 'development';
const userDataPath = app.getPath('userData');
const dbPath = path.join(userDataPath, 'salonsys.db');

let db: Database.Database;
let win: BrowserWindow | null = null;

// Initialize database
function initDatabase() {
  try {
    // Ensure the user data directory exists
    if (!fs.existsSync(userDataPath)) {
      fs.mkdirSync(userDataPath, { recursive: true });
    }

    db = new Database(dbPath);
    
    // Enable WAL mode for better performance
    db.pragma('journal_mode = WAL');
    
    // Create tables
    createTables();
    
    console.log('Database initialized at:', dbPath);
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

function createTables() {
  // Create all tables with proper schema
  const tables = [
    `CREATE TABLE IF NOT EXISTS employees (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      position TEXT NOT NULL,
      salary REAL NOT NULL,
      phone TEXT NOT NULL,
      email TEXT NOT NULL,
      address TEXT NOT NULL,
      hireDate TEXT NOT NULL,
      status TEXT NOT NULL CHECK (status IN ('active', 'inactive')),
      photo TEXT,
      notes TEXT,
      createdAt TEXT NOT NULL,
      updatedAt TEXT NOT NULL
    )`,
    
    `CREATE TABLE IF NOT EXISTS attendance (
      id TEXT PRIMARY KEY,
      employeeId TEXT NOT NULL,
      date TEXT NOT NULL,
      checkIn TEXT,
      checkOut TEXT,
      status TEXT NOT NULL CHECK (status IN ('present', 'absent', 'late', 'overtime')),
      notes TEXT,
      overtimeHours REAL,
      createdAt TEXT NOT NULL,
      updatedAt TEXT NOT NULL,
      FOREIGN KEY (employeeId) REFERENCES employees (id)
    )`,
    
    `CREATE TABLE IF NOT EXISTS advances (
      id TEXT PRIMARY KEY,
      employeeId TEXT NOT NULL,
      amount REAL NOT NULL,
      reason TEXT NOT NULL,
      date TEXT NOT NULL,
      status TEXT NOT NULL CHECK (status IN ('active', 'paid', 'cancelled')),
      createdAt TEXT NOT NULL,
      updatedAt TEXT NOT NULL,
      FOREIGN KEY (employeeId) REFERENCES employees (id)
    )`,
    
    `CREATE TABLE IF NOT EXISTS payroll (
      id TEXT PRIMARY KEY,
      employeeId TEXT NOT NULL,
      month TEXT NOT NULL,
      year INTEGER NOT NULL,
      baseSalary REAL NOT NULL,
      advances REAL NOT NULL,
      penalties REAL NOT NULL,
      overtimeBonus REAL NOT NULL,
      currentSalary REAL NOT NULL,
      status TEXT NOT NULL CHECK (status IN ('pending', 'processed', 'paid')),
      createdAt TEXT NOT NULL,
      updatedAt TEXT NOT NULL,
      FOREIGN KEY (employeeId) REFERENCES employees (id)
    )`,
    
    `CREATE TABLE IF NOT EXISTS services (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      duration INTEGER NOT NULL,
      price REAL NOT NULL,
      description TEXT NOT NULL,
      category TEXT NOT NULL CHECK (category IN ('hair', 'nail', 'facial', 'massage', 'other')),
      isActive INTEGER NOT NULL CHECK (isActive IN (0, 1)),
      createdAt TEXT NOT NULL,
      updatedAt TEXT NOT NULL
    )`,
    
    `CREATE TABLE IF NOT EXISTS packages (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT NOT NULL,
      services TEXT NOT NULL, -- JSON array of service IDs
      totalPrice REAL NOT NULL,
      discountedPrice REAL NOT NULL,
      isActive INTEGER NOT NULL CHECK (isActive IN (0, 1)),
      createdAt TEXT NOT NULL,
      updatedAt TEXT NOT NULL
    )`,
    
    `CREATE TABLE IF NOT EXISTS dresses (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT NOT NULL,
      rentalPrice REAL NOT NULL,
      availableColors TEXT NOT NULL, -- JSON array
      status TEXT NOT NULL CHECK (status IN ('available', 'rented', 'maintenance')),
      imageUrl TEXT,
      category TEXT NOT NULL CHECK (category IN ('wedding', 'evening', 'cocktail', 'formal', 'other')),
      createdAt TEXT NOT NULL,
      updatedAt TEXT NOT NULL
    )`,
    
    `CREATE TABLE IF NOT EXISTS customers (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      phone TEXT NOT NULL,
      email TEXT NOT NULL,
      address TEXT,
      notes TEXT,
      instagram TEXT,
      facebook TEXT,
      whatsapp TEXT,
      totalBookings INTEGER NOT NULL DEFAULT 0,
      totalSpent REAL NOT NULL DEFAULT 0,
      lastVisit TEXT,
      customerSince TEXT NOT NULL,
      favoriteServices TEXT NOT NULL, -- JSON array
      createdAt TEXT NOT NULL,
      updatedAt TEXT NOT NULL
    )`,
    
    `CREATE TABLE IF NOT EXISTS bookings (
      id TEXT PRIMARY KEY,
      customerId TEXT,
      customerName TEXT NOT NULL,
      title TEXT,
      date TEXT NOT NULL,
      startTime TEXT NOT NULL,
      endTime TEXT NOT NULL,
      employeeIds TEXT NOT NULL, -- JSON array
      serviceIds TEXT NOT NULL, -- JSON array
      packageIds TEXT NOT NULL, -- JSON array
      dressIds TEXT NOT NULL, -- JSON array
      dressRentalPeriods TEXT, -- JSON array
      subtotal REAL NOT NULL,
      discount REAL NOT NULL,
      deposit REAL NOT NULL,
      totalAmount REAL NOT NULL,
      amountDue REAL NOT NULL,
      status TEXT NOT NULL CHECK (status IN ('confirmed', 'completed', 'cancelled', 'pending')),
      notes TEXT,
      createdAt TEXT NOT NULL,
      updatedAt TEXT NOT NULL,
      FOREIGN KEY (customerId) REFERENCES customers (id)
    )`,
    
    `CREATE TABLE IF NOT EXISTS holidays (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      date TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('national', 'religious', 'company', 'other')),
      description TEXT,
      isRecurring INTEGER NOT NULL CHECK (isRecurring IN (0, 1)),
      createdAt TEXT NOT NULL,
      updatedAt TEXT NOT NULL
    )`,
    
    `CREATE TABLE IF NOT EXISTS dayoffs (
      id TEXT PRIMARY KEY,
      employeeId TEXT NOT NULL,
      date TEXT NOT NULL,
      reason TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('sick', 'vacation', 'personal', 'emergency', 'other')),
      status TEXT NOT NULL CHECK (status IN ('approved', 'pending', 'rejected')),
      approvedBy TEXT,
      notes TEXT,
      createdAt TEXT NOT NULL,
      updatedAt TEXT NOT NULL,
      FOREIGN KEY (employeeId) REFERENCES employees (id)
    )`,
    
    `CREATE TABLE IF NOT EXISTS settings (
      id TEXT PRIMARY KEY,
      workingDays TEXT NOT NULL, -- JSON array
      workingHours TEXT NOT NULL, -- JSON object
      holidays TEXT NOT NULL, -- JSON array
      attendanceRules TEXT NOT NULL, -- JSON object
      darkMode INTEGER NOT NULL CHECK (darkMode IN (0, 1)),
      rtlMode INTEGER NOT NULL CHECK (rtlMode IN (0, 1)),
      language TEXT,
      primaryColor TEXT,
      currency TEXT NOT NULL,
      timezone TEXT NOT NULL,
      updatedAt TEXT NOT NULL
    )`
  ];

  // Create indexes for better performance
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_attendance_employee ON attendance(employeeId)',
    'CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance(date)',
    'CREATE INDEX IF NOT EXISTS idx_advances_employee ON advances(employeeId)',
    'CREATE INDEX IF NOT EXISTS idx_advances_date ON advances(date)',
    'CREATE INDEX IF NOT EXISTS idx_advances_status ON advances(status)',
    'CREATE INDEX IF NOT EXISTS idx_payroll_employee ON payroll(employeeId)',
    'CREATE INDEX IF NOT EXISTS idx_payroll_month ON payroll(month)',
    'CREATE INDEX IF NOT EXISTS idx_payroll_year ON payroll(year)',
    'CREATE INDEX IF NOT EXISTS idx_services_category ON services(category)',
    'CREATE INDEX IF NOT EXISTS idx_services_active ON services(isActive)',
    'CREATE INDEX IF NOT EXISTS idx_packages_active ON packages(isActive)',
    'CREATE INDEX IF NOT EXISTS idx_dresses_category ON dresses(category)',
    'CREATE INDEX IF NOT EXISTS idx_dresses_status ON dresses(status)',
    'CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)',
    'CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)',
    'CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email)',
    'CREATE INDEX IF NOT EXISTS idx_bookings_customer ON bookings(customerId)',
    'CREATE INDEX IF NOT EXISTS idx_bookings_date ON bookings(date)',
    'CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status)',
    'CREATE INDEX IF NOT EXISTS idx_holidays_date ON holidays(date)',
    'CREATE INDEX IF NOT EXISTS idx_holidays_type ON holidays(type)',
    'CREATE INDEX IF NOT EXISTS idx_dayoffs_employee ON dayoffs(employeeId)',
    'CREATE INDEX IF NOT EXISTS idx_dayoffs_date ON dayoffs(date)',
    'CREATE INDEX IF NOT EXISTS idx_dayoffs_status ON dayoffs(status)'
  ];

  // Execute table creation
  tables.forEach(sql => {
    db.exec(sql);
  });

  // Execute index creation
  indexes.forEach(sql => {
    db.exec(sql);
  });
}

function createWindow(): void {
  win = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.cjs'),
      nodeIntegration: false,
      contextIsolation: true,
    },
    icon: path.join(process.env.VITE_PUBLIC, 'electron.png'),
    show: false, // Don't show until ready
    titleBarStyle: 'default',
  });

  // Show window when ready to prevent visual flash
  win.once('ready-to-show', () => {
    win?.show();
    
    if (isDev) {
      win?.webContents.openDevTools();
    }
  });

  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', new Date().toLocaleString());
  });

  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
  } else {
    win.loadFile(path.join(RENDERER_DIST, 'index.html'));
  }
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
    win = null;
  }
});

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC handlers for database operations
function setupIpcHandlers() {
  // Database operations
  ipcMain.handle('db:getAll', async (event, tableName: string) => {
    try {
      const stmt = db.prepare(`SELECT * FROM ${tableName}`);
      const rows = stmt.all();

      // Parse JSON fields for specific tables
      return rows.map(row => parseJsonFields(row, tableName));
    } catch (error) {
      console.error(`Error getting all from ${tableName}:`, error);
      throw error;
    }
  });

  ipcMain.handle('db:get', async (event, tableName: string, id: string) => {
    try {
      const stmt = db.prepare(`SELECT * FROM ${tableName} WHERE id = ?`);
      const row = stmt.get(id);

      return row ? parseJsonFields(row, tableName) : undefined;
    } catch (error) {
      console.error(`Error getting ${id} from ${tableName}:`, error);
      throw error;
    }
  });

  ipcMain.handle('db:add', async (event, tableName: string, data: any) => {
    try {
      const processedData = stringifyJsonFields(data, tableName);
      const columns = Object.keys(processedData);
      const placeholders = columns.map(() => '?').join(', ');
      const values = Object.values(processedData);

      const stmt = db.prepare(`INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`);
      stmt.run(...values);
    } catch (error) {
      console.error(`Error adding to ${tableName}:`, error);
      throw error;
    }
  });

  ipcMain.handle('db:update', async (event, tableName: string, data: any) => {
    try {
      const processedData = stringifyJsonFields(data, tableName);
      const { id, ...updateData } = processedData;
      const columns = Object.keys(updateData);
      const setClause = columns.map(col => `${col} = ?`).join(', ');
      const values = [...Object.values(updateData), id];

      const stmt = db.prepare(`UPDATE ${tableName} SET ${setClause} WHERE id = ?`);
      stmt.run(...values);
    } catch (error) {
      console.error(`Error updating ${tableName}:`, error);
      throw error;
    }
  });

  ipcMain.handle('db:delete', async (event, tableName: string, id: string) => {
    try {
      const stmt = db.prepare(`DELETE FROM ${tableName} WHERE id = ?`);
      stmt.run(id);
    } catch (error) {
      console.error(`Error deleting from ${tableName}:`, error);
      throw error;
    }
  });

  ipcMain.handle('db:getByIndex', async (event, tableName: string, indexField: string, value: any) => {
    try {
      const stmt = db.prepare(`SELECT * FROM ${tableName} WHERE ${indexField} = ?`);
      const rows = stmt.all(value);

      return rows.map(row => parseJsonFields(row, tableName));
    } catch (error) {
      console.error(`Error getting by index from ${tableName}:`, error);
      throw error;
    }
  });

  ipcMain.handle('db:clearAllData', async () => {
    try {
      const tables = ['employees', 'attendance', 'advances', 'payroll', 'services', 'packages',
                     'dresses', 'customers', 'bookings', 'settings', 'holidays', 'dayoffs'];

      tables.forEach(table => {
        db.prepare(`DELETE FROM ${table}`).run();
      });
    } catch (error) {
      console.error('Error clearing all data:', error);
      throw error;
    }
  });

  // File system operations
  ipcMain.handle('fs:showSaveDialog', async (event, options) => {
    const result = await dialog.showSaveDialog(win!, options);
    return result;
  });

  ipcMain.handle('fs:showOpenDialog', async (event, options) => {
    const result = await dialog.showOpenDialog(win!, options);
    return result;
  });

  ipcMain.handle('fs:writeFile', async (event, filePath: string, data: string) => {
    fs.writeFileSync(filePath, data, 'utf8');
  });

  ipcMain.handle('fs:readFile', async (event, filePath: string) => {
    return fs.readFileSync(filePath, 'utf8');
  });

  // App operations
  ipcMain.handle('app:getVersion', () => {
    return app.getVersion();
  });

  ipcMain.handle('app:getPath', (event, name: string) => {
    return app.getPath(name as any);
  });

  ipcMain.handle('app:quit', () => {
    app.quit();
  });

  // Window operations
  ipcMain.handle('window:minimize', () => {
    win?.minimize();
  });

  ipcMain.handle('window:maximize', () => {
    if (win?.isMaximized()) {
      win.unmaximize();
    } else {
      win?.maximize();
    }
  });

  ipcMain.handle('window:close', () => {
    win?.close();
  });

  ipcMain.handle('window:isMaximized', () => {
    return win?.isMaximized();
  });

  // Migration operations
  ipcMain.handle('db:migrateFromIndexedDB', async (event, indexedDBData: any) => {
    try {
      // Clear existing data first
      await clearAllTables();

      // Migrate each table
      for (const [tableName, records] of Object.entries(indexedDBData)) {
        if (Array.isArray(records)) {
          for (const record of records) {
            const processedData = stringifyJsonFields(record, tableName);
            const columns = Object.keys(processedData);
            const placeholders = columns.map(() => '?').join(', ');
            const values = Object.values(processedData);

            const stmt = db.prepare(`INSERT OR REPLACE INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`);
            stmt.run(...values);
          }
        }
      }

      console.log('Migration from IndexedDB completed successfully');
    } catch (error) {
      console.error('Error migrating from IndexedDB:', error);
      throw error;
    }
  });

  ipcMain.handle('db:exportData', async () => {
    try {
      const tables = ['employees', 'attendance', 'advances', 'payroll', 'services', 'packages',
                     'dresses', 'customers', 'bookings', 'settings', 'holidays', 'dayoffs'];

      const exportData: any = {};

      tables.forEach(table => {
        const stmt = db.prepare(`SELECT * FROM ${table}`);
        const rows = stmt.all();
        exportData[table] = rows.map(row => parseJsonFields(row, table));
      });

      return exportData;
    } catch (error) {
      console.error('Error exporting data:', error);
      throw error;
    }
  });

  ipcMain.handle('db:importData', async (event, data: any) => {
    try {
      // Clear existing data first
      await clearAllTables();

      // Import each table
      for (const [tableName, records] of Object.entries(data)) {
        if (Array.isArray(records)) {
          for (const record of records) {
            const processedData = stringifyJsonFields(record, tableName);
            const columns = Object.keys(processedData);
            const placeholders = columns.map(() => '?').join(', ');
            const values = Object.values(processedData);

            const stmt = db.prepare(`INSERT OR REPLACE INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`);
            stmt.run(...values);
          }
        }
      }

      console.log('Data import completed successfully');
    } catch (error) {
      console.error('Error importing data:', error);
      throw error;
    }
  });
}

// Helper function to clear all tables
function clearAllTables() {
  const tables = ['employees', 'attendance', 'advances', 'payroll', 'services', 'packages',
                 'dresses', 'customers', 'bookings', 'settings', 'holidays', 'dayoffs'];

  tables.forEach(table => {
    db.prepare(`DELETE FROM ${table}`).run();
  });
}

// Helper functions for JSON field handling
function parseJsonFields(row: any, tableName: string): any {
  const jsonFields = getJsonFields(tableName);
  const parsed = { ...row };

  jsonFields.forEach(field => {
    if (parsed[field] && typeof parsed[field] === 'string') {
      try {
        parsed[field] = JSON.parse(parsed[field]);
      } catch (e) {
        console.warn(`Failed to parse JSON field ${field} in ${tableName}:`, e);
      }
    }
  });

  // Convert boolean fields
  const booleanFields = getBooleanFields(tableName);
  booleanFields.forEach(field => {
    if (parsed[field] !== undefined) {
      parsed[field] = Boolean(parsed[field]);
    }
  });

  return parsed;
}

function stringifyJsonFields(data: any, tableName: string): any {
  const jsonFields = getJsonFields(tableName);
  const processed = { ...data };

  jsonFields.forEach(field => {
    if (processed[field] && typeof processed[field] !== 'string') {
      processed[field] = JSON.stringify(processed[field]);
    }
  });

  // Convert boolean fields
  const booleanFields = getBooleanFields(tableName);
  booleanFields.forEach(field => {
    if (processed[field] !== undefined) {
      processed[field] = processed[field] ? 1 : 0;
    }
  });

  return processed;
}

function getJsonFields(tableName: string): string[] {
  const jsonFieldsMap: { [key: string]: string[] } = {
    packages: ['services'],
    dresses: ['availableColors'],
    customers: ['favoriteServices'],
    bookings: ['employeeIds', 'serviceIds', 'packageIds', 'dressIds', 'dressRentalPeriods'],
    settings: ['workingDays', 'workingHours', 'holidays', 'attendanceRules']
  };

  return jsonFieldsMap[tableName] || [];
}

function getBooleanFields(tableName: string): string[] {
  const booleanFieldsMap: { [key: string]: string[] } = {
    services: ['isActive'],
    packages: ['isActive'],
    holidays: ['isRecurring'],
    settings: ['darkMode', 'rtlMode']
  };

  return booleanFieldsMap[tableName] || [];
}

app.whenReady().then(() => {
  initDatabase();
  setupIpcHandlers();
  createWindow();
});
