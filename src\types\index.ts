export interface Employee {
  id: string;
  name: string;
  position: string;
  salary: number;
  phone: string;
  email: string;
  address: string;
  hireDate: string;
  status: 'active' | 'inactive';
  photo?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AttendanceRecord {
  id: string;
  employeeId: string;
  date: string;
  checkIn?: string;
  checkOut?: string;
  status: 'present' | 'absent' | 'late' | 'overtime';
  notes?: string;
  overtimeHours?: number;
  createdAt: string;
  updatedAt: string;
}

export interface EmployeeAdvance {
  id: string;
  employeeId: string;
  amount: number;
  reason: string;
  date: string;
  status: 'active' | 'paid' | 'cancelled';
  createdAt: string;
  updatedAt: string;
}

export interface PayrollRecord {
  id: string;
  employeeId: string;
  month: string;
  year: number;
  baseSalary: number;
  advances: number;
  penalties: number;
  overtimeBonus: number;
  currentSalary: number;
  status: 'pending' | 'processed' | 'paid';
  createdAt: string;
  updatedAt: string;
}

export interface Service {
  id: string;
  name: string;
  duration: number; // in minutes
  price: number;
  description: string;
  category: 'hair' | 'nail' | 'facial' | 'massage' | 'other';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ServicePackage {
  id: string;
  name: string;
  description: string;
  services: string[]; // service IDs
  totalPrice: number;
  discountedPrice: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface DressRental {
  id: string;
  name: string;
  description: string;
  rentalPrice: number;
  availableColors: string[];
  status: 'available' | 'rented' | 'maintenance';
  imageUrl?: string;
  category: 'wedding' | 'evening' | 'cocktail' | 'formal' | 'other';
  createdAt: string;
  updatedAt: string;
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  email: string;
  address?: string;
  notes?: string;
  instagram?: string;
  facebook?: string;
  whatsapp?: string;
  totalBookings: number;
  totalSpent: number;
  lastVisit?: string;
  customerSince: string;
  favoriteServices: string[];
  createdAt: string;
  updatedAt: string;
}

export interface DressRentalPeriod {
  dressId: string;
  startDate: string;
  endDate: string;
}

export interface Booking {
  id: string;
  customerId?: string;
  customerName: string;
  title?: string;
  date: string;
  startTime: string;
  endTime: string;
  employeeIds: string[];
  serviceIds: string[];
  packageIds: string[];
  dressIds: string[];
  dressRentalPeriods?: DressRentalPeriod[]; // New field for dress rental periods
  subtotal: number;
  discount: number;
  deposit: number;
  totalAmount: number;
  amountDue: number;
  status: 'confirmed' | 'completed' | 'cancelled' | 'pending';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Holiday {
  id: string;
  name: string;
  date: string;
  type: 'national' | 'religious' | 'company' | 'other';
  description?: string;
  isRecurring: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface EmployeeDayOff {
  id: string;
  employeeId: string;
  date: string;
  reason: string;
  type: 'sick' | 'vacation' | 'personal' | 'emergency' | 'other';
  status: 'approved' | 'pending' | 'rejected';
  approvedBy?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Settings {
  id: string;
  workingDays: string[];
  workingHours: {
    start: string;
    end: string;
  };
  holidays: string[]; // Array of holiday IDs
  attendanceRules: {
    latePenalty: number;
    absencePenalty: number;
    allowanceTime: number; // minutes
    overtimeReward: number;
    lateDaysForPenalty?: number; // Number of late days before penalty applies
    absentDaysForPenalty?: number; // Number of absent days before penalty applies
  };
  darkMode: boolean;
  rtlMode: boolean;
  language?: string; // 'en' | 'ar'
  primaryColor?: string; // Color theme identifier
  currency: string;
  timezone: string;
  updatedAt: string;
}

export type Page = 'dashboard' | 'employees' | 'attendance' | 'payroll' | 'services' | 'bookings' | 'customers' | 'financial' | 'settings';