// TypeScript declarations for Electron API

export interface ElectronAPI {
  database: {
    getAll: (tableName: string) => Promise<any[]>;
    get: (tableName: string, id: string) => Promise<any>;
    add: (tableName: string, data: any) => Promise<void>;
    update: (tableName: string, data: any) => Promise<void>;
    delete: (tableName: string, id: string) => Promise<void>;
    getByIndex: (tableName: string, indexField: string, value: any) => Promise<any[]>;
    clearAllData: () => Promise<void>;
    migrateFromIndexedDB: (data: any) => Promise<void>;
    exportData: () => Promise<any>;
    importData: (data: any) => Promise<void>;
  };
  fs: {
    showSaveDialog: (options: any) => Promise<any>;
    showOpenDialog: (options: any) => Promise<any>;
    writeFile: (filePath: string, data: string) => Promise<void>;
    readFile: (filePath: string) => Promise<string>;
  };
  app: {
    getVersion: () => Promise<string>;
    getPath: (name: string) => Promise<string>;
    quit: () => Promise<void>;
  };
  window: {
    minimize: () => Promise<void>;
    maximize: () => Promise<void>;
    close: () => Promise<void>;
    isMaximized: () => Promise<boolean>;
  };
  on: (channel: string, callback: Function) => void;
  removeAllListeners: (channel: string) => void;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
