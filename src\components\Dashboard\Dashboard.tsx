import React, { useState, useEffect } from 'react';
import { TrendingUp, Users, Calendar, DollarSign, Plus, UserPlus, Eye, RefreshCw, EyeOff } from 'lucide-react';
import { useBookings, useEmployees, useServices, useSettings } from '../../hooks/useDatabase';
import { format, startOfMonth, endOfMonth, startOfYear, endOfYear, eachDayOfInterval, eachWeekOfInterval, eachMonthOfInterval, subDays, subWeeks, subMonths, subYears, endOfWeek } from 'date-fns';
import { useTranslation } from '../../utils/translations';
import QuickBookingModal from './QuickBookingModal';

export default function Dashboard() {
  const { bookings } = useBookings();
  const { employees } = useEmployees();
  const { services } = useServices();
  const { settings } = useSettings(true);
  const t = useTranslation(settings?.language || 'en');
  const [selectedPeriod, setSelectedPeriod] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('weekly');
  const [showQuickBooking, setShowQuickBooking] = useState(false);
  const [revenueVisible, setRevenueVisible] = useState(false);

  const today = new Date();
  const activeEmployees = employees.filter(emp => emp.status === 'active');
  
  // Only count completed bookings for revenue (exclude cancelled)
  const completedBookings = bookings.filter(booking => booking.status === 'completed');
  const confirmedBookings = bookings.filter(booking => booking.status === 'confirmed');
  
  // Calculate total revenue from completed bookings only
  const totalRevenue = completedBookings.reduce((sum, booking) => sum + booking.totalAmount, 0);
  
  // Get today's appointments
  const todayString = format(today, 'yyyy-MM-dd');
  const todayAppointments = bookings.filter(booking => booking.date === todayString);
  
  // Find most popular service
  const serviceBookings = completedBookings.reduce((acc, booking) => {
    booking.serviceIds.forEach(serviceId => {
      acc[serviceId] = (acc[serviceId] || 0) + 1;
    });
    return acc;
  }, {} as Record<string, number>);
  
  const mostPopularServiceId = Object.entries(serviceBookings).sort(([,a], [,b]) => b - a)[0]?.[0];
  const mostPopularService = services.find(s => s.id === mostPopularServiceId);

  // Generate chart data based on selected period
  const getChartData = () => {
    const now = new Date();
    let intervals: Date[] = [];
    let labels: string[] = [];

    switch (selectedPeriod) {
      case 'daily':
        // Last 7 days
        intervals = eachDayOfInterval({
          start: subDays(now, 6),
          end: now
        });
        labels = intervals.map(date => format(date, 'EEE'));
        break;
      case 'weekly':
        // Last 4 weeks
        intervals = eachWeekOfInterval({
          start: subWeeks(now, 3),
          end: now
        });
        labels = intervals.map((date, index) => `Week ${index + 1}`);
        break;
      case 'monthly':
        // Last 6 months
        intervals = eachMonthOfInterval({
          start: subMonths(now, 5),
          end: now
        });
        labels = intervals.map(date => format(date, 'MMM'));
        break;
      case 'yearly':
        // Last 3 years
        intervals = [
          subYears(now, 2),
          subYears(now, 1),
          now
        ];
        labels = intervals.map(date => format(date, 'yyyy'));
        break;
    }

    // Calculate revenue for each interval from completed bookings only
    const revenueData = intervals.map(intervalStart => {
      let intervalEnd: Date;
      
      switch (selectedPeriod) {
        case 'daily':
          intervalEnd = new Date(intervalStart.getTime() + 24 * 60 * 60 * 1000 - 1);
          break;
        case 'weekly':
          intervalEnd = endOfWeek(intervalStart);
          break;
        case 'monthly':
          intervalEnd = endOfMonth(intervalStart);
          break;
        case 'yearly':
          intervalEnd = endOfYear(intervalStart);
          break;
      }

      const periodBookings = completedBookings.filter(booking => {
        const bookingDate = new Date(booking.date);
        return bookingDate >= intervalStart && bookingDate <= intervalEnd;
      });

      return periodBookings.reduce((sum, booking) => sum + booking.totalAmount, 0);
    });

    return { labels, data: revenueData };
  };

  const chartData = getChartData();
  const maxRevenue = Math.max(...chartData.data, 1); // Ensure at least 1 to avoid division by zero

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t.dashboard}</h1>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="daily">{t.daily}</option>
            <option value="weekly">{t.weekly}</option>
            <option value="monthly">{t.monthly}</option>
            <option value="yearly">{t.yearly}</option>
          </select>
          <button className="flex items-center space-x-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
            <RefreshCw className="h-4 w-4" />
            <span>{t.refresh}</span>
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Revenue Card with Blur Effect */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow relative">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className={`transition-all duration-300 ${!revenueVisible ? 'filter blur-sm' : ''}`}>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">${totalRevenue.toFixed(2)}</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{t.totalRevenue}</p>
              <p className="text-xs text-gray-500">{t.fromCompletedBookingsOnly}</p>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <button
                onClick={() => setRevenueVisible(!revenueVisible)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                title={revenueVisible ? "Hide revenue" : "Show revenue"}
              >
                {revenueVisible ? (
                  <EyeOff className="h-4 w-4 text-gray-500" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-500" />
                )}
              </button>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{bookings.filter(b => b.status !== 'cancelled').length}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{t.totalBookings}</p>
              <p className="text-xs text-gray-500">{t.completedAndConfirmed}</p>
            </div>
            <div className="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
              <Calendar className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{activeEmployees.length}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{t.activeEmployees}</p>
              <p className="text-xs text-gray-500">{t.currentlyWorking}</p>
            </div>
            <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <Users className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{mostPopularService?.name || 'Hair Cut'}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{t.popularService}</p>
              <p className="text-xs text-gray-500">{t.mostBookedService}</p>
            </div>
            <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
              <TrendingUp className="h-6 w-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>{t.quickActions}</span>
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">{t.quicklyPerformCommonTasks}</p>
          </div>

          <div className="grid grid-cols-1 gap-3">
            <button
              onClick={() => setShowQuickBooking(true)}
              className="flex items-center space-x-3 p-4 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <Plus className="h-5 w-5" />
              <span className="font-medium">{t.quickNewBooking}</span>
            </button>
            
            <button className="flex items-center space-x-3 p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <UserPlus className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              <span className="font-medium text-gray-900 dark:text-white">{t.addCustomer}</span>
            </button>
            
            <button className="flex items-center space-x-3 p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <Calendar className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              <span className="font-medium text-gray-900 dark:text-white">{t.viewCalendar}</span>
            </button>
          </div>
        </div>

        {/* Today's Appointments */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{t.todaysAppointments}</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">{todayAppointments.length} {t.appointmentsToday}</p>
          </div>

          {todayAppointments.length > 0 ? (
            <div className="space-y-4">
              {todayAppointments.slice(0, 3).map((appointment) => (
                <div key={appointment.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium text-gray-900 dark:text-white">
                        {appointment.title || `${appointment.customerName} Appointment`}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        appointment.status === 'confirmed'
                          ? 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300'
                          : appointment.status === 'completed'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                      }`}>
                        {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Today, {appointment.startTime}
                    </p>
                  </div>
                </div>
              ))}
              
              {todayAppointments.length > 3 && (
                <div className="text-center">
                  <button className="text-primary-600 dark:text-primary-400 text-sm hover:underline">
                    {t.viewAll} {todayAppointments.length} {t.appointments}
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400 mb-4">{t.noAppointmentsScheduled}</p>
              <button
                onClick={() => setShowQuickBooking(true)}
                className="inline-flex items-center space-x-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
              >
                <Plus className="h-4 w-4" />
                <span>{t.createNewBooking}</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Revenue Overview Chart with Primary Color */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)} {t.revenueOverview}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t.revenueTrends}
          </p>
        </div>

        {/* Enhanced Chart with Primary Color */}
        <div className="h-80 flex items-end justify-between space-x-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          {chartData.labels.map((label, index) => {
            const value = chartData.data[index];
            const height = maxRevenue > 0 ? Math.max((value / maxRevenue) * 100, 2) : 2; // Minimum 2% height for visibility
            const isCurrentPeriod = index === chartData.labels.length - 1;
            
            return (
              <div key={index} className="flex-1 flex flex-col items-center group relative">
                <div className="w-full flex flex-col items-center relative h-64">
                  {/* Value Label on Hover */}
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 absolute -top-8 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs px-2 py-1 rounded shadow-lg z-10 whitespace-nowrap">
                    ${value.toFixed(0)}
                  </div>
                  
                  {/* Spacer to push bar to bottom */}
                  <div className="flex-1"></div>
                  
                  {/* Bar */}
                  <div 
                    className={`w-full rounded-t-lg transition-all duration-500 hover:opacity-80 cursor-pointer relative ${
                      isCurrentPeriod 
                        ? 'bg-primary-600 shadow-lg' 
                        : 'bg-primary-300 dark:bg-primary-700'
                    }`}
                    style={{ 
                      height: `${height}%`,
                      minHeight: '8px'
                    }}
                  >
                    {/* Gradient overlay for enhanced visual appeal */}
                    <div 
                      className={`w-full h-full rounded-t-lg ${
                        isCurrentPeriod 
                          ? 'bg-gradient-to-t from-primary-700 to-primary-500' 
                          : 'bg-gradient-to-t from-primary-400 to-primary-200 dark:from-primary-800 dark:to-primary-600'
                      }`}
                    />
                  </div>
                </div>
                
                {/* Period Label */}
                <span className="text-xs text-gray-500 dark:text-gray-400 mt-3 font-medium text-center">
                  {label}
                </span>
                
                {/* Value below label */}
                <span className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                  ${value.toFixed(0)}
                </span>
              </div>
            );
          })}
        </div>

        {/* Chart Statistics */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-center">
            <div className="text-lg font-bold text-primary-600 dark:text-primary-400">
              ${Math.max(...chartData.data).toFixed(0)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Highest Period</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-gray-600 dark:text-gray-400">
              ${(chartData.data.reduce((a, b) => a + b, 0) / chartData.data.length).toFixed(0)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Average</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600 dark:text-green-400">
              ${chartData.data.reduce((a, b) => a + b, 0).toFixed(0)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Total Period</div>
          </div>
        </div>
      </div>

      {/* Quick Booking Modal */}
      {showQuickBooking && (
        <QuickBookingModal onClose={() => setShowQuickBooking(false)} />
      )}
    </div>
  );
}