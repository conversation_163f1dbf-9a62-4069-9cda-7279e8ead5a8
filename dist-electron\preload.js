"use strict";
const { contextBridge, ipcRenderer } = require("electron");
contextBridge.exposeInMainWorld("electronAPI", {
  // Database operations
  database: {
    // Generic CRUD operations
    getAll: (tableName) => ipcRenderer.invoke("db:getAll", tableName),
    get: (tableName, id) => ipcRenderer.invoke("db:get", tableName, id),
    add: (tableName, data) => ipcRenderer.invoke("db:add", tableName, data),
    update: (tableName, data) => ipcRenderer.invoke("db:update", tableName, data),
    delete: (tableName, id) => ipcRenderer.invoke("db:delete", tableName, id),
    getByIndex: (tableName, indexField, value) => ipcRenderer.invoke("db:getByIndex", tableName, indexField, value),
    clearAllData: () => ipc<PERSON>enderer.invoke("db:clearAllData"),
    // Migration operations
    migrateFromIndexedDB: (data) => ipcRenderer.invoke("db:migrateFromIndexedDB", data),
    exportData: () => ipcRenderer.invoke("db:exportData"),
    importData: (data) => ipcRenderer.invoke("db:importData", data)
  },
  // File system operations
  fs: {
    showSaveDialog: (options) => ipcRenderer.invoke("fs:showSaveDialog", options),
    showOpenDialog: (options) => ipcRenderer.invoke("fs:showOpenDialog", options),
    writeFile: (filePath, data) => ipcRenderer.invoke("fs:writeFile", filePath, data),
    readFile: (filePath) => ipcRenderer.invoke("fs:readFile", filePath)
  },
  // App information
  app: {
    getVersion: () => ipcRenderer.invoke("app:getVersion"),
    getPath: (name) => ipcRenderer.invoke("app:getPath", name),
    quit: () => ipcRenderer.invoke("app:quit")
  },
  // Window operations
  window: {
    minimize: () => ipcRenderer.invoke("window:minimize"),
    maximize: () => ipcRenderer.invoke("window:maximize"),
    close: () => ipcRenderer.invoke("window:close"),
    isMaximized: () => ipcRenderer.invoke("window:isMaximized")
  },
  // Event listeners
  on: (channel, callback) => {
    ipcRenderer.on(channel, (event, ...args) => callback(...args));
  },
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});
