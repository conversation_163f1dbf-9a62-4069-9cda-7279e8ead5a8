// SQLite Database Service for Electron
// This service provides the same interface as the IndexedDB service but uses SQLite through Electron's main process

class SalonSQLiteDatabase {
  private isElectron: boolean;
  private electronAPI: any;

  constructor() {
    this.isElectron = typeof window !== 'undefined' && window.electronAPI;
    this.electronAPI = this.isElectron ? window.electronAPI : null;
  }

  async init(): Promise<void> {
    if (!this.isElectron) {
      throw new Error('SQLite database can only be used in Electron environment');
    }
    
    // Database is already initialized in the main process
    // This method exists for compatibility with the IndexedDB interface
    return Promise.resolve();
  }

  async getAll<T>(storeName: string): Promise<T[]> {
    if (!this.electronAPI) {
      throw new Error('Electron API not available');
    }
    
    try {
      return await this.electronAPI.database.getAll(storeName);
    } catch (error) {
      console.error(`Error getting all from ${storeName}:`, error);
      throw error;
    }
  }

  async get<T>(storeName: string, id: string): Promise<T | undefined> {
    if (!this.electronAPI) {
      throw new Error('Electron API not available');
    }
    
    try {
      return await this.electronAPI.database.get(storeName, id);
    } catch (error) {
      console.error(`Error getting ${id} from ${storeName}:`, error);
      throw error;
    }
  }

  async add<T>(storeName: string, data: T): Promise<void> {
    if (!this.electronAPI) {
      throw new Error('Electron API not available');
    }
    
    try {
      await this.electronAPI.database.add(storeName, data);
    } catch (error) {
      console.error(`Error adding to ${storeName}:`, error);
      throw error;
    }
  }

  async update<T>(storeName: string, data: T): Promise<void> {
    if (!this.electronAPI) {
      throw new Error('Electron API not available');
    }
    
    try {
      await this.electronAPI.database.update(storeName, data);
    } catch (error) {
      console.error(`Error updating ${storeName}:`, error);
      throw error;
    }
  }

  async delete(storeName: string, id: string): Promise<void> {
    if (!this.electronAPI) {
      throw new Error('Electron API not available');
    }
    
    try {
      await this.electronAPI.database.delete(storeName, id);
    } catch (error) {
      console.error(`Error deleting from ${storeName}:`, error);
      throw error;
    }
  }

  async getByIndex<T>(storeName: string, indexName: string, value: any): Promise<T[]> {
    if (!this.electronAPI) {
      throw new Error('Electron API not available');
    }
    
    try {
      return await this.electronAPI.database.getByIndex(storeName, indexName, value);
    } catch (error) {
      console.error(`Error getting by index from ${storeName}:`, error);
      throw error;
    }
  }

  async clearAllData(): Promise<void> {
    if (!this.electronAPI) {
      throw new Error('Electron API not available');
    }
    
    try {
      await this.electronAPI.database.clearAllData();
    } catch (error) {
      console.error('Error clearing all data:', error);
      throw error;
    }
  }

  // Migration helper methods
  async migrateFromIndexedDB(indexedDBData: any): Promise<void> {
    if (!this.electronAPI) {
      throw new Error('Electron API not available');
    }
    
    try {
      await this.electronAPI.database.migrateFromIndexedDB(indexedDBData);
    } catch (error) {
      console.error('Error migrating from IndexedDB:', error);
      throw error;
    }
  }

  async exportData(): Promise<any> {
    if (!this.electronAPI) {
      throw new Error('Electron API not available');
    }
    
    try {
      return await this.electronAPI.database.exportData();
    } catch (error) {
      console.error('Error exporting data:', error);
      throw error;
    }
  }

  async importData(data: any): Promise<void> {
    if (!this.electronAPI) {
      throw new Error('Electron API not available');
    }
    
    try {
      await this.electronAPI.database.importData(data);
    } catch (error) {
      console.error('Error importing data:', error);
      throw error;
    }
  }

  // Utility method to check if running in Electron
  isElectronEnvironment(): boolean {
    return this.isElectron;
  }
}

export const sqliteDatabase = new SalonSQLiteDatabase();
