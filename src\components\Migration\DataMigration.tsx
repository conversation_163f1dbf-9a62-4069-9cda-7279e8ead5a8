import React, { useState, useEffect } from 'react';
import { Database, Download, Upload, AlertCircle, CheckCircle, Loader } from 'lucide-react';

interface MigrationProps {
  onMigrationComplete: () => void;
}

const DataMigration: React.FC<MigrationProps> = ({ onMigrationComplete }) => {
  const [migrationStatus, setMigrationStatus] = useState<'idle' | 'checking' | 'migrating' | 'complete' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [indexedDBData, setIndexedDBData] = useState<any>(null);
  const [isElectron, setIsElectron] = useState(false);

  useEffect(() => {
    setIsElectron(typeof window !== 'undefined' && !!window.electronAPI);
    if (typeof window !== 'undefined' && !!window.electronAPI) {
      checkForExistingData();
    }
  }, []);

  const checkForExistingData = async () => {
    setMigrationStatus('checking');
    
    try {
      // Check if IndexedDB has data
      const hasIndexedDBData = await checkIndexedDBData();
      
      if (hasIndexedDBData) {
        setMigrationStatus('idle');
      } else {
        setMigrationStatus('complete');
        onMigrationComplete();
      }
    } catch (error) {
      console.error('Error checking for existing data:', error);
      setMigrationStatus('complete');
      onMigrationComplete();
    }
  };

  const checkIndexedDBData = async (): Promise<boolean> => {
    return new Promise((resolve) => {
      const request = indexedDB.open('SalonSysDB');
      
      request.onerror = () => resolve(false);
      
      request.onsuccess = () => {
        const db = request.result;
        
        if (db.objectStoreNames.length === 0) {
          resolve(false);
          return;
        }

        // Check if any store has data
        const transaction = db.transaction(Array.from(db.objectStoreNames), 'readonly');
        let hasData = false;
        let checkedStores = 0;
        const totalStores = db.objectStoreNames.length;

        Array.from(db.objectStoreNames).forEach(storeName => {
          const store = transaction.objectStore(storeName);
          const countRequest = store.count();
          
          countRequest.onsuccess = () => {
            if (countRequest.result > 0) {
              hasData = true;
            }
            checkedStores++;
            
            if (checkedStores === totalStores) {
              resolve(hasData);
            }
          };
          
          countRequest.onerror = () => {
            checkedStores++;
            if (checkedStores === totalStores) {
              resolve(hasData);
            }
          };
        });
      };
    });
  };

  const exportIndexedDBData = async (): Promise<any> => {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('SalonSysDB');
      
      request.onerror = () => reject(new Error('Failed to open IndexedDB'));
      
      request.onsuccess = () => {
        const db = request.result;
        const storeNames = Array.from(db.objectStoreNames);
        const exportData: any = {};
        
        if (storeNames.length === 0) {
          resolve(exportData);
          return;
        }

        const transaction = db.transaction(storeNames, 'readonly');
        let completedStores = 0;

        storeNames.forEach(storeName => {
          const store = transaction.objectStore(storeName);
          const getAllRequest = store.getAll();
          
          getAllRequest.onsuccess = () => {
            exportData[storeName] = getAllRequest.result;
            completedStores++;
            
            if (completedStores === storeNames.length) {
              resolve(exportData);
            }
          };
          
          getAllRequest.onerror = () => {
            exportData[storeName] = [];
            completedStores++;
            
            if (completedStores === storeNames.length) {
              resolve(exportData);
            }
          };
        });
      };
    });
  };

  const performMigration = async () => {
    if (!isElectron) {
      setErrorMessage('Migration is only available in the desktop version');
      setMigrationStatus('error');
      return;
    }

    setMigrationStatus('migrating');
    setErrorMessage('');

    try {
      // Export data from IndexedDB
      const data = await exportIndexedDBData();
      setIndexedDBData(data);

      // Migrate to SQLite
      await window.electronAPI.database.migrateFromIndexedDB(data);

      setMigrationStatus('complete');
      onMigrationComplete();
    } catch (error) {
      console.error('Migration failed:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Migration failed');
      setMigrationStatus('error');
    }
  };

  const exportData = async () => {
    try {
      const data = await exportIndexedDBData();
      const dataStr = JSON.stringify(data, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `salonsys-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
      setErrorMessage('Failed to export data');
    }
  };

  const importData = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      const data = JSON.parse(text);
      
      if (isElectron) {
        await window.electronAPI.database.importData(data);
        setMigrationStatus('complete');
        onMigrationComplete();
      } else {
        setErrorMessage('Import is only available in the desktop version');
      }
    } catch (error) {
      console.error('Import failed:', error);
      setErrorMessage('Failed to import data');
    }
  };

  if (migrationStatus === 'checking') {
    return (
      <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader className="animate-spin h-12 w-12 text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Checking for existing data...</p>
        </div>
      </div>
    );
  }

  if (migrationStatus === 'complete') {
    return null; // Migration is complete, let the app continue
  }

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="text-center mb-6">
          <Database className="h-16 w-16 text-blue-600 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Data Migration
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            We found existing data that needs to be migrated to the new database format.
          </p>
        </div>

        {migrationStatus === 'error' && (
          <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
              <p className="text-red-800 dark:text-red-200 text-sm">{errorMessage}</p>
            </div>
          </div>
        )}

        <div className="space-y-4">
          <button
            onClick={performMigration}
            disabled={migrationStatus === 'migrating'}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
          >
            {migrationStatus === 'migrating' ? (
              <>
                <Loader className="animate-spin h-5 w-5 mr-2" />
                Migrating Data...
              </>
            ) : (
              <>
                <Database className="h-5 w-5 mr-2" />
                Migrate Data
              </>
            )}
          </button>

          <div className="flex space-x-2">
            <button
              onClick={exportData}
              className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
            >
              <Download className="h-4 w-4 mr-2" />
              Export Backup
            </button>

            <label className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center cursor-pointer">
              <Upload className="h-4 w-4 mr-2" />
              Import Data
              <input
                type="file"
                accept=".json"
                onChange={importData}
                className="hidden"
              />
            </label>
          </div>

          <button
            onClick={onMigrationComplete}
            className="w-full text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium py-2 transition-colors"
          >
            Skip Migration
          </button>
        </div>
      </div>
    </div>
  );
};

export default DataMigration;
