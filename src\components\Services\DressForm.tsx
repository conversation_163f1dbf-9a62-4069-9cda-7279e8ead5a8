import React, { useState, useEffect } from 'react';
import { X, Plus } from 'lucide-react';
import { useDressRentals, useSettings } from '../../hooks/useDatabase';
import { useTranslation } from '../../utils/translations';
import { DressRental } from '../../types';

interface DressFormProps {
  dress?: DressRental | null;
  onClose: () => void;
}

export default function DressForm({ dress, onClose }: DressFormProps) {
  const { addDress, updateDress } = useDressRentals();
  const { settings } = useSettings(true);
  const t = useTranslation(settings?.language || 'en');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    rentalPrice: '',
    category: 'wedding' as 'wedding' | 'evening' | 'cocktail' | 'formal' | 'other',
    status: 'available' as 'available' | 'rented' | 'maintenance',
    availableColors: [] as string[],
    photos: [] as string[],
  });

  const colors = [
    { name: 'Black', hex: '#000000' },
    { name: 'White', hex: '#FFFFFF' },
    { name: 'Red', hex: '#EF4444' },
    { name: 'Blue', hex: '#3B82F6' },
    { name: 'Green', hex: '#10B981' },
    { name: 'Pink', hex: '#EC4899' },
    { name: 'Purple', hex: '#8B5CF6' },
    { name: 'Yellow', hex: '#F59E0B' },
    { name: 'Orange', hex: '#F97316' },
    { name: 'Brown', hex: '#A16207' },
    { name: 'Gray', hex: '#6B7280' },
    { name: 'Navy', hex: '#1E40AF' },
    { name: 'Burgundy', hex: '#991B1B' },
    { name: 'Ivory', hex: '#FFFBEB' }
  ];

  useEffect(() => {
    if (dress) {
      setFormData({
        name: dress.name,
        description: dress.description,
        rentalPrice: dress.rentalPrice.toString(),
        category: dress.category,
        status: dress.status,
        availableColors: dress.availableColors,
        photos: dress.imageUrl ? [dress.imageUrl] : [],
      });
    }
  }, [dress]);

  const handleColorToggle = (colorName: string) => {
    setFormData(prev => ({
      ...prev,
      availableColors: prev.availableColors.includes(colorName)
        ? prev.availableColors.filter(c => c !== colorName)
        : [...prev.availableColors, colorName]
    }));
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const photoUrl = event.target?.result as string;
        setFormData(prev => ({
          ...prev,
          photos: [...prev.photos, photoUrl]
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const dressData = {
      name: formData.name,
      description: formData.description,
      rentalPrice: parseFloat(formData.rentalPrice),
      category: formData.category,
      status: formData.status,
      availableColors: formData.availableColors,
      imageUrl: formData.photos[0] || undefined,
    };

    if (dress) {
      await updateDress(dress.id, dressData);
    } else {
      await addDress(dressData);
    }
    
    onClose();
  };

  const isRTL = settings?.language === 'ar' || settings?.rtlMode;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={isRTL ? 'text-right' : 'text-left'}>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {dress ? t.editDress : t.addNewDress}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t.addDressDetailsIncludingPhotosAndColors}
            </p>
          </div>
          <button onClick={onClose} className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t.dressName}
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              required
              placeholder={t.enterDressName}
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.rentalPriceDollar}
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.rentalPrice}
                onChange={(e) => setFormData(prev => ({ ...prev, rentalPrice: e.target.value }))}
                required
                placeholder="0"
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
              />
            </div>

            <div>
              <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.status}
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
              >
                <option value="available">{t.available}</option>
                <option value="rented">{t.rented}</option>
                <option value="maintenance">{t.maintenance}</option>
              </select>
            </div>
          </div>

          <div>
            <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t.availableColors}
            </label>
            <div className="grid grid-cols-4 gap-3">
              {colors.map((color) => (
                <label key={color.name} className={`flex items-center space-x-2 cursor-pointer ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={formData.availableColors.includes(color.name)}
                      onChange={() => handleColorToggle(color.name)}
                      className="sr-only"
                    />
                    <div 
                      className={`w-6 h-6 rounded-full border-2 transition-all duration-200 ${
                        formData.availableColors.includes(color.name) 
                          ? 'border-gray-900 dark:border-white shadow-lg scale-110' 
                          : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                      } ${color.name === 'White' ? 'border-gray-400' : ''}`}
                      style={{ 
                        backgroundColor: color.hex,
                        boxShadow: color.name === 'White' ? 'inset 0 0 0 1px rgba(0,0,0,0.1)' : undefined
                      }}
                    >
                      {formData.availableColors.includes(color.name) && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className={`w-2 h-2 rounded-full ${
                            color.name === 'White' || color.name === 'Yellow' || color.name === 'Ivory' 
                              ? 'bg-gray-800' 
                              : 'bg-white'
                          }`}></div>
                        </div>
                      )}
                    </div>
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">{color.name}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t.photos}
            </label>
            <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6">
              <div className="text-center">
                <label className="cursor-pointer">
                  <div className={`flex flex-col items-center space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                    <Plus className="h-8 w-8 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">{t.addPhoto}</span>
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoUpload}
                    className="hidden"
                  />
                </label>
              </div>
              
              {formData.photos.length > 0 && (
                <div className="mt-4 grid grid-cols-3 gap-2">
                  {formData.photos.map((photo, index) => (
                    <img
                      key={index}
                      src={photo}
                      alt={`Dress photo ${index + 1}`}
                      className="w-full h-20 object-cover rounded-lg"
                    />
                  ))}
                </div>
              )}
            </div>
          </div>

          <div>
            <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t.description}
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={4}
              placeholder={t.enterDressDescription}
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
            />
          </div>

          <div className={`flex justify-end space-x-3 pt-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              {t.cancel}
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors"
            >
              {dress ? t.saveDressChanges : t.addDress}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}