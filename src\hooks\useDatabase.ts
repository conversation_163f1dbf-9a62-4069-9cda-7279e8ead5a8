import { useEffect, useState } from 'react';
import { database } from '../services/database';
import { Employee, AttendanceRecord, EmployeeAdvance, Settings, PayrollRecord, Service, ServicePackage, DressRental, Customer, Booking, Holiday, EmployeeDayOff } from '../types';

// Global flag to prevent multiple initializations
let isInitializing = false;
let hasInitialized = false;

export function useEmployees() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);

  const loadEmployees = async () => {
    try {
      const data = await database.getAll<Employee>('employees');
      setEmployees(data);
    } catch (error) {
      console.error('Error loading employees:', error);
    } finally {
      setLoading(false);
    }
  };

  const addEmployee = async (employee: Omit<Employee, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newEmployee: Employee = {
      ...employee,
      id: crypto.randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await database.add('employees', newEmployee);
    await loadEmployees();
  };

  const updateEmployee = async (id: string, updates: Partial<Employee>) => {
    const existing = await database.get<Employee>('employees', id);
    if (existing) {
      const updated: Employee = {
        ...existing,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      await database.update('employees', updated);
      await loadEmployees();
    }
  };

  const deleteEmployee = async (id: string) => {
    await database.delete('employees', id);
    await loadEmployees();
  };

  useEffect(() => {
    const initializeEmployees = async () => {
      if (isInitializing || hasInitialized) {
        await loadEmployees();
        return;
      }

      isInitializing = true;
      await loadEmployees();
      
      // Only add sample data if no employees exist
      const currentEmployees = await database.getAll<Employee>('employees');
      if (currentEmployees.length === 0) {
        const sampleEmployees = [
          {
            name: 'Emma Johnson',
            position: 'Senior Stylist',
            salary: 2500,
            phone: '+****************',
            email: '<EMAIL>',
            address: '123 Main St, City, State 12345',
            hireDate: '2023-01-15',
            status: 'active' as const,
            notes: 'Experienced stylist specializing in color treatments',
          },
          {
            name: 'David Smith',
            position: 'Beautician',
            salary: 2200,
            phone: '+****************',
            email: '<EMAIL>',
            address: '456 Oak Ave, City, State 67890',
            hireDate: '2023-03-20',
            status: 'active' as const,
            notes: 'Expert in nail care and facial treatments',
          }
        ];

        for (const emp of sampleEmployees) {
          await addEmployee(emp);
        }
      }
      
      hasInitialized = true;
      isInitializing = false;
    };

    initializeEmployees();
  }, []);

  return {
    employees,
    loading,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    refreshEmployees: loadEmployees,
  };
}

export function useAttendance() {
  const [attendance, setAttendance] = useState<AttendanceRecord[]>([]);
  const [loading, setLoading] = useState(true);

  const loadAttendance = async () => {
    try {
      const data = await database.getAll<AttendanceRecord>('attendance');
      setAttendance(data);
    } catch (error) {
      console.error('Error loading attendance:', error);
    } finally {
      setLoading(false);
    }
  };

  const addAttendanceRecord = async (record: Omit<AttendanceRecord, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newRecord: AttendanceRecord = {
      ...record,
      id: crypto.randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await database.add('attendance', newRecord);
    await loadAttendance();
  };

  const updateAttendanceRecord = async (id: string, updates: Partial<AttendanceRecord>) => {
    const existing = await database.get<AttendanceRecord>('attendance', id);
    if (existing) {
      const updated: AttendanceRecord = {
        ...existing,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      await database.update('attendance', updated);
      await loadAttendance();
    }
  };

  const getAttendanceByEmployee = async (employeeId: string) => {
    return database.getByIndex<AttendanceRecord>('attendance', 'employeeId', employeeId);
  };

  const getAttendanceByDate = async (date: string) => {
    return database.getByIndex<AttendanceRecord>('attendance', 'date', date);
  };

  useEffect(() => {
    loadAttendance();
  }, []);

  return {
    attendance,
    loading,
    addAttendanceRecord,
    updateAttendanceRecord,
    getAttendanceByEmployee,
    getAttendanceByDate,
    refreshAttendance: loadAttendance,
  };
}

export function useAdvances() {
  const [advances, setAdvances] = useState<EmployeeAdvance[]>([]);
  const [loading, setLoading] = useState(true);

  const loadAdvances = async () => {
    try {
      const data = await database.getAll<EmployeeAdvance>('advances');
      setAdvances(data);
    } catch (error) {
      console.error('Error loading advances:', error);
    } finally {
      setLoading(false);
    }
  };

  const addAdvance = async (advance: Omit<EmployeeAdvance, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newAdvance: EmployeeAdvance = {
      ...advance,
      id: crypto.randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await database.add('advances', newAdvance);
    await loadAdvances();
  };

  const updateAdvance = async (id: string, updates: Partial<EmployeeAdvance>) => {
    const existing = await database.get<EmployeeAdvance>('advances', id);
    if (existing) {
      const updated: EmployeeAdvance = {
        ...existing,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      await database.update('advances', updated);
      await loadAdvances();
    }
  };

  const getAdvancesByEmployee = async (employeeId: string) => {
    return database.getByIndex<EmployeeAdvance>('advances', 'employeeId', employeeId);
  };

  useEffect(() => {
    loadAdvances();
  }, []);

  return {
    advances,
    loading,
    addAdvance,
    updateAdvance,
    getAdvancesByEmployee,
    refreshAdvances: loadAdvances,
  };
}

export function usePayroll() {
  const [payroll, setPayroll] = useState<PayrollRecord[]>([]);
  const [loading, setLoading] = useState(true);

  const loadPayroll = async () => {
    try {
      const data = await database.getAll<PayrollRecord>('payroll');
      setPayroll(data);
    } catch (error) {
      console.error('Error loading payroll:', error);
    } finally {
      setLoading(false);
    }
  };

  const generatePayroll = async (month: string) => {
    console.log('Generating payroll for', month);
    // Implementation for generating payroll reports
  };

  const resetMonthlyPayroll = async (month: string) => {
    try {
      // Mark all advances for the month as processed
      const advances = await database.getAll<EmployeeAdvance>('advances');
      const monthlyAdvances = advances.filter(advance => 
        advance.date.startsWith(month) && advance.status === 'active'
      );

      for (const advance of monthlyAdvances) {
        await database.update('advances', {
          ...advance,
          status: 'paid',
          updatedAt: new Date().toISOString(),
        });
      }

      console.log('Monthly payroll reset completed for', month);
    } catch (error) {
      console.error('Error resetting monthly payroll:', error);
      throw error;
    }
  };

  useEffect(() => {
    loadPayroll();
  }, []);

  return {
    payroll,
    loading,
    generatePayroll,
    resetMonthlyPayroll,
    refreshPayroll: loadPayroll,
  };
}

export function useServices() {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);

  const loadServices = async () => {
    try {
      const data = await database.getAll<Service>('services');
      setServices(data);
    } catch (error) {
      console.error('Error loading services:', error);
    } finally {
      setLoading(false);
    }
  };

  const addService = async (service: Omit<Service, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newService: Service = {
      ...service,
      id: crypto.randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await database.add('services', newService);
    await loadServices();
  };

  const updateService = async (id: string, updates: Partial<Service>) => {
    const existing = await database.get<Service>('services', id);
    if (existing) {
      const updated: Service = {
        ...existing,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      await database.update('services', updated);
      await loadServices();
    }
  };

  const deleteService = async (id: string) => {
    await database.delete('services', id);
    await loadServices();
  };

  useEffect(() => {
    const initializeServices = async () => {
      await loadServices();
      
      // Only add sample data if no services exist and not already initializing
      if (hasInitialized) return;
      
      const currentServices = await database.getAll<Service>('services');
      if (currentServices.length === 0) {
        const sampleServices = [
          {
            name: 'Hair Cut',
            duration: 30,
            price: 35,
            description: 'Professional hair cutting service',
            category: 'hair' as const,
            isActive: true,
          },
          {
            name: 'Hair Coloring',
            duration: 120,
            price: 120,
            description: 'Full hair coloring service',
            category: 'hair' as const,
            isActive: true,
          },
          {
            name: 'Manicure',
            duration: 45,
            price: 40,
            description: 'Professional nail care',
            category: 'nail' as const,
            isActive: true,
          }
        ];

        for (const service of sampleServices) {
          await addService(service);
        }
      }
    };

    initializeServices();
  }, []);

  return {
    services,
    loading,
    addService,
    updateService,
    deleteService,
    refreshServices: loadServices,
  };
}

export function useServicePackages() {
  const [packages, setPackages] = useState<ServicePackage[]>([]);
  const [loading, setLoading] = useState(true);

  const loadPackages = async () => {
    try {
      const data = await database.getAll<ServicePackage>('packages');
      setPackages(data);
    } catch (error) {
      console.error('Error loading packages:', error);
    } finally {
      setLoading(false);
    }
  };

  const addPackage = async (pkg: Omit<ServicePackage, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newPackage: ServicePackage = {
      ...pkg,
      id: crypto.randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await database.add('packages', newPackage);
    await loadPackages();
  };

  const updatePackage = async (id: string, updates: Partial<ServicePackage>) => {
    const existing = await database.get<ServicePackage>('packages', id);
    if (existing) {
      const updated: ServicePackage = {
        ...existing,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      await database.update('packages', updated);
      await loadPackages();
    }
  };

  const deletePackage = async (id: string) => {
    await database.delete('packages', id);
    await loadPackages();
  };

  useEffect(() => {
    const initializePackages = async () => {
      await loadPackages();
      
      // Only add sample data if no packages exist and services are available
      if (hasInitialized) return;
      
      const currentPackages = await database.getAll<ServicePackage>('packages');
      if (currentPackages.length === 0) {
        // Wait for services to be loaded first
        setTimeout(async () => {
          const services = await database.getAll<Service>('services');
          if (services.length >= 2) {
            const hairCutService = services.find(s => s.name === 'Hair Cut');
            const hairColorService = services.find(s => s.name === 'Hair Coloring');
            
            if (hairCutService && hairColorService) {
              const samplePackage = {
                name: 'Complete Makeover',
                description: 'Hair cut, coloring, and styling package',
                services: [hairCutService.id, hairColorService.id],
                totalPrice: hairCutService.price + hairColorService.price,
                discountedPrice: 180,
                isActive: true,
              };

              await addPackage(samplePackage);
            }
          }
        }, 2000);
      }
    };

    initializePackages();
  }, []);

  return {
    packages,
    loading,
    addPackage,
    updatePackage,
    deletePackage,
    refreshPackages: loadPackages,
  };
}

export function useDressRentals() {
  const [dresses, setDresses] = useState<DressRental[]>([]);
  const [loading, setLoading] = useState(true);

  const loadDresses = async () => {
    try {
      const data = await database.getAll<DressRental>('dresses');
      setDresses(data);
    } catch (error) {
      console.error('Error loading dresses:', error);
    } finally {
      setLoading(false);
    }
  };

  const addDress = async (dress: Omit<DressRental, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newDress: DressRental = {
      ...dress,
      id: crypto.randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await database.add('dresses', newDress);
    await loadDresses();
  };

  const updateDress = async (id: string, updates: Partial<DressRental>) => {
    const existing = await database.get<DressRental>('dresses', id);
    if (existing) {
      const updated: DressRental = {
        ...existing,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      await database.update('dresses', updated);
      await loadDresses();
    }
  };

  const deleteDress = async (id: string) => {
    await database.delete('dresses', id);
    await loadDresses();
  };

  // Check if a dress is available for a specific date range
  const isDressAvailable = async (dressId: string, startDate: string, endDate: string, excludeBookingId?: string) => {
    try {
      const bookings = await database.getAll<Booking>('bookings');
      
      // Filter active bookings (not cancelled) that include this dress
      const activeBookings = bookings.filter(booking => 
        booking.status !== 'cancelled' && 
        booking.dressIds.includes(dressId) &&
        booking.id !== excludeBookingId // Exclude current booking when editing
      );

      // Check for date conflicts
      for (const booking of activeBookings) {
        if (booking.dressRentalPeriods) {
          const dressRentalPeriod = booking.dressRentalPeriods.find(period => period.dressId === dressId);
          if (dressRentalPeriod) {
            const existingStart = new Date(dressRentalPeriod.startDate);
            const existingEnd = new Date(dressRentalPeriod.endDate);
            const newStart = new Date(startDate);
            const newEnd = new Date(endDate);

            // Check for overlap
            if (newStart <= existingEnd && newEnd >= existingStart) {
              return false; // Conflict found
            }
          }
        }
      }

      return true; // No conflicts
    } catch (error) {
      console.error('Error checking dress availability:', error);
      return false;
    }
  };

  // Update dress statuses based on current bookings
  const updateDressStatuses = async () => {
    try {
      const bookings = await database.getAll<Booking>('bookings');
      const currentDresses = await database.getAll<DressRental>('dresses');
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Start of today

      for (const dress of currentDresses) {
        let isCurrentlyRented = false;

        // Check if dress is currently rented
        for (const booking of bookings) {
          if (booking.status !== 'cancelled' && booking.dressIds.includes(dress.id)) {
            if (booking.dressRentalPeriods) {
              const dressRentalPeriod = booking.dressRentalPeriods.find(period => period.dressId === dress.id);
              if (dressRentalPeriod) {
                const startDate = new Date(dressRentalPeriod.startDate);
                const endDate = new Date(dressRentalPeriod.endDate);
                startDate.setHours(0, 0, 0, 0);
                endDate.setHours(23, 59, 59, 999);

                // Check if today falls within the rental period
                if (today >= startDate && today <= endDate) {
                  isCurrentlyRented = true;
                  break;
                }
              }
            }
          }
        }

        // Update dress status if needed
        const newStatus = isCurrentlyRented ? 'rented' : 'available';
        if (dress.status !== newStatus && dress.status !== 'maintenance') {
          await updateDress(dress.id, { status: newStatus });
        }
      }
    } catch (error) {
      console.error('Error updating dress statuses:', error);
    }
  };

  useEffect(() => {
    const initializeDresses = async () => {
      await loadDresses();
      
      // Update dress statuses based on current bookings
      await updateDressStatuses();
      
      // Only add sample data if no dresses exist
      if (hasInitialized) return;
      
      const currentDresses = await database.getAll<DressRental>('dresses');
      if (currentDresses.length === 0) {
        const sampleDresses = [
          {
            name: 'Evening Gown - Elegant Black',
            description: 'Elegant evening gown perfect for special occasions',
            rentalPrice: 150,
            availableColors: ['Black', 'Navy'],
            status: 'available' as const,
            category: 'evening' as const,
            imageUrl: 'https://images.pexels.com/photos/1021693/pexels-photo-1021693.jpeg?auto=compress&cs=tinysrgb&w=400'
          },
          {
            name: 'Wedding Dress - Classic White',
            description: 'Beautiful classic wedding dress',
            rentalPrice: 300,
            availableColors: ['White', 'Ivory'],
            status: 'available' as const,
            category: 'wedding' as const,
            imageUrl: 'https://images.pexels.com/photos/1043474/pexels-photo-1043474.jpeg?auto=compress&cs=tinysrgb&w=400'
          }
        ];

        for (const dress of sampleDresses) {
          await addDress(dress);
        }
      }
    };

    initializeDresses();
  }, []);

  // Set up periodic status updates
  useEffect(() => {
    const interval = setInterval(updateDressStatuses, 60000); // Update every minute
    return () => clearInterval(interval);
  }, []);

  return {
    dresses,
    loading,
    addDress,
    updateDress,
    deleteDress,
    isDressAvailable,
    updateDressStatuses,
    refreshDresses: loadDresses,
  };
}

export function useBookings() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);

  const loadBookings = async () => {
    try {
      const data = await database.getAll<Booking>('bookings');
      setBookings(data);
    } catch (error) {
      console.error('Error loading bookings:', error);
    } finally {
      setLoading(false);
    }
  };

  const addBooking = async (booking: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newBooking: Booking = {
      ...booking,
      id: crypto.randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await database.add('bookings', newBooking);
    await loadBookings();
    
    // Update dress statuses after adding booking
    await updateDressStatusesAfterBookingChange();
  };

  const updateBooking = async (id: string, updates: Partial<Booking>) => {
    const existing = await database.get<Booking>('bookings', id);
    if (existing) {
      const updated: Booking = {
        ...existing,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      await database.update('bookings', updated);
      await loadBookings();
      
      // Update dress statuses after updating booking
      await updateDressStatusesAfterBookingChange();
    }
  };

  const deleteBooking = async (id: string) => {
    await database.delete('bookings', id);
    await loadBookings();
    
    // Update dress statuses after deleting booking
    await updateDressStatusesAfterBookingChange();
  };

  // Helper function to update dress statuses after booking changes
  const updateDressStatusesAfterBookingChange = async () => {
    try {
      const dresses = await database.getAll<DressRental>('dresses');
      const allBookings = await database.getAll<Booking>('bookings');
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      for (const dress of dresses) {
        let isCurrentlyRented = false;

        // Check if dress is currently rented
        for (const booking of allBookings) {
          if (booking.status !== 'cancelled' && booking.dressIds.includes(dress.id)) {
            if (booking.dressRentalPeriods) {
              const dressRentalPeriod = booking.dressRentalPeriods.find(period => period.dressId === dress.id);
              if (dressRentalPeriod) {
                const startDate = new Date(dressRentalPeriod.startDate);
                const endDate = new Date(dressRentalPeriod.endDate);
                startDate.setHours(0, 0, 0, 0);
                endDate.setHours(23, 59, 59, 999);

                if (today >= startDate && today <= endDate) {
                  isCurrentlyRented = true;
                  break;
                }
              }
            }
          }
        }

        // Update dress status if needed (don't change maintenance status)
        const newStatus = isCurrentlyRented ? 'rented' : 'available';
        if (dress.status !== newStatus && dress.status !== 'maintenance') {
          await database.update('dresses', {
            ...dress,
            status: newStatus,
            updatedAt: new Date().toISOString(),
          });
        }
      }
    } catch (error) {
      console.error('Error updating dress statuses:', error);
    }
  };

  useEffect(() => {
    const initializeBookings = async () => {
      await loadBookings();
      
      // Only add sample data if no bookings exist
      if (hasInitialized) return;
      
      const currentBookings = await database.getAll<Booking>('bookings');
      if (currentBookings.length === 0) {
        const sampleBookings = [
          {
            customerId: undefined,
            customerName: 'Jane Smith',
            title: 'Hair Cut',
            date: '2025-06-28',
            startTime: '09:00',
            endTime: '10:00',
            employeeIds: [],
            serviceIds: [],
            packageIds: [],
            dressIds: [],
            subtotal: 35,
            discount: 0,
            deposit: 0,
            totalAmount: 35,
            amountDue: 35,
            status: 'confirmed' as const,
            notes: 'Regular customer, prefers natural look',
          },
          {
            customerId: undefined,
            customerName: 'Robert Johnson',
            title: 'Complete Makeover',
            date: '2025-06-28',
            startTime: '14:00',
            endTime: '17:00',
            employeeIds: [],
            serviceIds: [],
            packageIds: [],
            dressIds: [],
            subtotal: 330,
            discount: 0,
            deposit: 0,
            totalAmount: 330,
            amountDue: 330,
            status: 'completed' as const,
            notes: 'Special occasion - wedding guest',
          }
        ];

        for (const booking of sampleBookings) {
          await addBooking(booking);
        }
      }
    };

    initializeBookings();
  }, []);

  return {
    bookings,
    loading,
    addBooking,
    updateBooking,
    deleteBooking,
    refreshBookings: loadBookings,
  };
}

export function useCustomers() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);

  const loadCustomers = async () => {
    try {
      const data = await database.getAll<Customer>('customers');
      setCustomers(data);
    } catch (error) {
      console.error('Error loading customers:', error);
    } finally {
      setLoading(false);
    }
  };

  const addCustomer = async (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newCustomer: Customer = {
      ...customer,
      id: crypto.randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await database.add('customers', newCustomer);
    await loadCustomers();
  };

  const updateCustomer = async (id: string, updates: Partial<Customer>) => {
    const existing = await database.get<Customer>('customers', id);
    if (existing) {
      const updated: Customer = {
        ...existing,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      await database.update('customers', updated);
      await loadCustomers();
    }
  };

  const deleteCustomer = async (id: string) => {
    await database.delete('customers', id);
    await loadCustomers();
  };

  useEffect(() => {
    const initializeCustomers = async () => {
      await loadCustomers();
      
      // Only add sample data if no customers exist
      if (hasInitialized) return;
      
      const currentCustomers = await database.getAll<Customer>('customers');
      if (currentCustomers.length === 0) {
        const sampleCustomers = [
          {
            name: 'Jane Smith',
            phone: '+****************',
            email: '<EMAIL>',
            address: '123 Main St, City, State 12345',
            notes: 'Prefers natural hair colors, allergic to certain chemicals',
            instagram: '@janesmith',
            facebook: 'jane.smith',
            whatsapp: '+15551234567',
            totalBookings: 12,
            totalSpent: 1450,
            lastVisit: '2024-01-10',
            customerSince: '2023-06-15',
            favoriteServices: ['Hair Styling', 'Hair Coloring'],
          },
          {
            name: 'Robert Johnson',
            phone: '+****************',
            email: '<EMAIL>',
            address: '456 Oak Ave, City, State 67890',
            notes: 'Regular customer, books monthly appointments',
            instagram: '',
            facebook: '',
            whatsapp: '+15559876543',
            totalBookings: 8,
            totalSpent: 680,
            lastVisit: '2024-01-08',
            customerSince: '2023-08-20',
            favoriteServices: ['Hair Cut'],
          }
        ];

        for (const customer of sampleCustomers) {
          await addCustomer(customer);
        }
      }
    };

    initializeCustomers();
  }, []);

  return {
    customers,
    loading,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    refreshCustomers: loadCustomers,
  };
}

export function useHolidays() {
  const [holidays, setHolidays] = useState<Holiday[]>([]);
  const [loading, setLoading] = useState(true);

  const loadHolidays = async () => {
    try {
      const data = await database.getAll<Holiday>('holidays');
      setHolidays(data);
    } catch (error) {
      console.error('Error loading holidays:', error);
    } finally {
      setLoading(false);
    }
  };

  const addHoliday = async (holiday: Omit<Holiday, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newHoliday: Holiday = {
      ...holiday,
      id: crypto.randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await database.add('holidays', newHoliday);
    await loadHolidays();
  };

  const updateHoliday = async (id: string, updates: Partial<Holiday>) => {
    const existing = await database.get<Holiday>('holidays', id);
    if (existing) {
      const updated: Holiday = {
        ...existing,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      await database.update('holidays', updated);
      await loadHolidays();
    }
  };

  const deleteHoliday = async (id: string) => {
    await database.delete('holidays', id);
    await loadHolidays();
  };

  const getHolidaysByDate = async (date: string) => {
    return database.getByIndex<Holiday>('holidays', 'date', date);
  };

  useEffect(() => {
    const initializeHolidays = async () => {
      await loadHolidays();
      
      // Only add sample holidays if none exist
      if (hasInitialized) return;
      
      const currentHolidays = await database.getAll<Holiday>('holidays');
      if (currentHolidays.length === 0) {
        const currentYear = new Date().getFullYear();
        const sampleHolidays = [
          {
            name: 'New Year\'s Day',
            date: `${currentYear}-01-01`,
            type: 'national' as const,
            description: 'New Year celebration',
            isRecurring: true,
          },
          {
            name: 'Independence Day',
            date: `${currentYear}-07-04`,
            type: 'national' as const,
            description: 'National Independence Day',
            isRecurring: true,
          },
          {
            name: 'Christmas Day',
            date: `${currentYear}-12-25`,
            type: 'national' as const,
            description: 'Christmas celebration',
            isRecurring: true,
          }
        ];

        for (const holiday of sampleHolidays) {
          await addHoliday(holiday);
        }
      }
    };

    initializeHolidays();
  }, []);

  return {
    holidays,
    loading,
    addHoliday,
    updateHoliday,
    deleteHoliday,
    getHolidaysByDate,
    refreshHolidays: loadHolidays,
  };
}

export function useEmployeeDayOffs() {
  const [dayOffs, setDayOffs] = useState<EmployeeDayOff[]>([]);
  const [loading, setLoading] = useState(true);

  const loadDayOffs = async () => {
    try {
      const data = await database.getAll<EmployeeDayOff>('dayoffs');
      setDayOffs(data);
    } catch (error) {
      console.error('Error loading day-offs:', error);
    } finally {
      setLoading(false);
    }
  };

  const addDayOff = async (dayOff: Omit<EmployeeDayOff, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newDayOff: EmployeeDayOff = {
      ...dayOff,
      id: crypto.randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await database.add('dayoffs', newDayOff);
    await loadDayOffs();
  };

  const updateDayOff = async (id: string, updates: Partial<EmployeeDayOff>) => {
    const existing = await database.get<EmployeeDayOff>('dayoffs', id);
    if (existing) {
      const updated: EmployeeDayOff = {
        ...existing,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      await database.update('dayoffs', updated);
      await loadDayOffs();
    }
  };

  const deleteDayOff = async (id: string) => {
    await database.delete('dayoffs', id);
    await loadDayOffs();
  };

  const getDayOffsByEmployee = async (employeeId: string) => {
    return database.getByIndex<EmployeeDayOff>('dayoffs', 'employeeId', employeeId);
  };

  const getDayOffsByDate = async (date: string) => {
    return database.getByIndex<EmployeeDayOff>('dayoffs', 'date', date);
  };

  useEffect(() => {
    loadDayOffs();
  }, []);

  return {
    dayOffs,
    loading,
    addDayOff,
    updateDayOff,
    deleteDayOff,
    getDayOffsByEmployee,
    getDayOffsByDate,
    refreshDayOffs: loadDayOffs,
  };
}

export function useSettings(isDbInitialized: boolean = false) {
  const [settings, setSettings] = useState<Settings | null>(null);
  const [loading, setLoading] = useState(true);

  const defaultSettings: Settings = {
    id: 'main',
    workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
    workingHours: {
      start: '09:00',
      end: '18:00',
    },
    holidays: [],
    attendanceRules: {
      latePenalty: 50,
      absencePenalty: 200,
      allowanceTime: 15,
      overtimeReward: 25,
    },
    darkMode: false,
    rtlMode: false,
    language: 'en',
    primaryColor: 'blue',
    currency: 'USD',
    timezone: 'UTC',
    updatedAt: new Date().toISOString(),
  };

  const loadSettings = async () => {
    if (!isDbInitialized) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const data = await database.get<Settings>('settings', 'main');
      if (data) {
        setSettings(data);
      } else {
        try {
          await database.add('settings', defaultSettings);
          setSettings(defaultSettings);
        } catch (addError) {
          const retryData = await database.get<Settings>('settings', 'main');
          setSettings(retryData || defaultSettings);
        }
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      setSettings(defaultSettings);
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = async (updates: Partial<Settings>) => {
    if (!settings || !isDbInitialized) return;

    try {
      const updated: Settings = {
        ...settings,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      await database.update('settings', updated);
      setSettings(updated);
    } catch (error) {
      console.error('Error updating settings:', error);
    }
  };

  useEffect(() => {
    if (isDbInitialized) {
      loadSettings();
    }
  }, [isDbInitialized]);

  return {
    settings,
    loading,
    updateSettings,
  };
}