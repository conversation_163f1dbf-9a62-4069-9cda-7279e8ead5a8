/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class', // Enable class-based dark mode
  theme: {
    extend: {
      colors: {
        primary: {
          50: 'var(--primary-50)',
          100: 'var(--primary-100)',
          200: 'var(--primary-200)',
          300: 'var(--primary-300)',
          400: 'var(--primary-400)',
          500: 'var(--primary-500)',
          600: 'var(--primary-600)',
          700: 'var(--primary-700)',
          800: 'var(--primary-800)',
          900: 'var(--primary-900)',
        },
      },
    },
  },
  plugins: [],
  // Add RTL support
  corePlugins: {
    // Enable all core plugins including direction utilities
  },
  // Add custom utilities for RTL support
  safelist: [
    'rtl',
    'ltr',
    // Add primary color classes to safelist
    'bg-primary-50', 'bg-primary-100', 'bg-primary-200', 'bg-primary-300', 'bg-primary-400',
    'bg-primary-500', 'bg-primary-600', 'bg-primary-700', 'bg-primary-800', 'bg-primary-900',
    'text-primary-50', 'text-primary-100', 'text-primary-200', 'text-primary-300', 'text-primary-400',
    'text-primary-500', 'text-primary-600', 'text-primary-700', 'text-primary-800', 'text-primary-900',
    'border-primary-50', 'border-primary-100', 'border-primary-200', 'border-primary-300', 'border-primary-400',
    'border-primary-500', 'border-primary-600', 'border-primary-700', 'border-primary-800', 'border-primary-900',
    'hover:bg-primary-50', 'hover:bg-primary-100', 'hover:bg-primary-200', 'hover:bg-primary-300', 'hover:bg-primary-400',
    'hover:bg-primary-500', 'hover:bg-primary-600', 'hover:bg-primary-700', 'hover:bg-primary-800', 'hover:bg-primary-900',
    'hover:text-primary-50', 'hover:text-primary-100', 'hover:text-primary-200', 'hover:text-primary-300', 'hover:text-primary-400',
    'hover:text-primary-500', 'hover:text-primary-600', 'hover:text-primary-700', 'hover:text-primary-800', 'hover:text-primary-900',
    'dark:bg-primary-50', 'dark:bg-primary-100', 'dark:bg-primary-200', 'dark:bg-primary-300', 'dark:bg-primary-400',
    'dark:bg-primary-500', 'dark:bg-primary-600', 'dark:bg-primary-700', 'dark:bg-primary-800', 'dark:bg-primary-900',
    'dark:text-primary-50', 'dark:text-primary-100', 'dark:text-primary-200', 'dark:text-primary-300', 'dark:text-primary-400',
    'dark:text-primary-500', 'dark:text-primary-600', 'dark:text-primary-700', 'dark:text-primary-800', 'dark:text-primary-900',
    'focus:ring-primary-500', 'focus:border-primary-500',
  ],
};