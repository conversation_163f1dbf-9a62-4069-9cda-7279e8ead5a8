{"name": "salonsys", "private": true, "version": "1.0.0", "main": "dist-electron/main.js", "type": "module", "scripts": {"dev": "vite", "build": "npm run build:renderer && npm run build:electron", "build:renderer": "vite build", "build:electron": "vite build --config vite.electron.config.ts", "lint": "eslint .", "preview": "vite preview", "electron": "npm run build:electron && electron .", "electron:dev": "concurrently \"npm run dev\" \"npm run build:electron && wait-on http://localhost:5173 && electron .\"", "electron:pack": "npm run build && electron-builder", "electron:dist": "npm run build && electron-builder --publish=never", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"better-sqlite3": "^12.2.0", "date-fns": "^3.0.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/better-sqlite3": "^7.6.13", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.2.0", "electron": "^37.2.2", "electron-builder": "^26.0.12", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "wait-on": "^8.0.3"}, "build": {"appId": "com.salonsys.app", "productName": "SalonSys", "directories": {"output": "dist-electron-pack"}, "files": ["dist/**/*", "dist-electron/**/*", "node_modules/**/*"], "extraResources": [{"from": "resources", "to": "resources", "filter": ["**/*"]}], "win": {"target": "nsis", "icon": "resources/icon.ico"}, "mac": {"target": "dmg", "icon": "resources/icon.icns"}, "linux": {"target": "AppImage", "icon": "resources/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}