import React, { useState, useEffect } from 'react';
import { Plus, Search, Scissors, Package, Shirt, Edit, Trash2 } from 'lucide-react';
import { useServices, useServicePackages, useDressRentals, useSettings, useBookings } from '../../hooks/useDatabase';
import { useTranslation } from '../../utils/translations';
import ServiceForm from './ServiceForm';
import PackageForm from './PackageForm';
import DressForm from './DressForm';

export default function ServiceManagement() {
  const { services, deleteService } = useServices();
  const { packages, deletePackage } = useServicePackages();
  const { dresses, deleteDress, updateDressStatuses } = useDressRentals();
  const { bookings } = useBookings();
  const { settings } = useSettings(true);
  const t = useTranslation(settings?.language || 'en');
  const [activeTab, setActiveTab] = useState<'services' | 'packages' | 'dresses'>('services');
  const [searchTerm, setSearchTerm] = useState('');
  const [showServiceForm, setShowServiceForm] = useState(false);
  const [showPackageForm, setShowPackageForm] = useState(false);
  const [showDressForm, setShowDressForm] = useState(false);
  const [editingService, setEditingService] = useState(null);
  const [editingPackage, setEditingPackage] = useState(null);
  const [editingDress, setEditingDress] = useState(null);
  const [selectedColors, setSelectedColors] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState('');

  const colors = [
    { name: 'Black', hex: '#000000' },
    { name: 'White', hex: '#FFFFFF' },
    { name: 'Red', hex: '#EF4444' },
    { name: 'Blue', hex: '#3B82F6' },
    { name: 'Green', hex: '#10B981' },
    { name: 'Pink', hex: '#EC4899' },
    { name: 'Purple', hex: '#8B5CF6' },
    { name: 'Yellow', hex: '#F59E0B' },
    { name: 'Orange', hex: '#F97316' },
    { name: 'Brown', hex: '#A16207' },
    { name: 'Gray', hex: '#6B7280' },
    { name: 'Navy', hex: '#1E40AF' },
    { name: 'Burgundy', hex: '#991B1B' },
    { name: 'Ivory', hex: '#FFFBEB' }
  ];

  // Update dress statuses when component mounts or bookings change
  useEffect(() => {
    if (updateDressStatuses) {
      updateDressStatuses();
    }
  }, [bookings, updateDressStatuses]);

  const filteredServices = services.filter(service =>
    service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredDresses = dresses.filter(dress => {
    const matchesSearch = dress.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      dress.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesColor = selectedColors.length === 0 || 
      selectedColors.some(color => dress.availableColors.includes(color));
    
    const matchesStatus = !statusFilter || dress.status === statusFilter;
    
    return matchesSearch && matchesColor && matchesStatus;
  });

  const handleColorToggle = (colorName: string) => {
    setSelectedColors(prev =>
      prev.includes(colorName)
        ? prev.filter(c => c !== colorName)
        : [...prev, colorName]
    );
  };

  const handleDeleteService = async (serviceId: string) => {
    if (confirm('Are you sure you want to delete this service?')) {
      await deleteService(serviceId);
    }
  };

  const handleDeletePackage = async (packageId: string) => {
    if (confirm('Are you sure you want to delete this package?')) {
      await deletePackage(packageId);
    }
  };

  const handleDeleteDress = async (dressId: string) => {
    if (confirm('Are you sure you want to delete this dress?')) {
      await deleteDress(dressId);
    }
  };

  const handleEditService = (service: any) => {
    setEditingService(service);
    setShowServiceForm(true);
  };

  const handleEditPackage = (pkg: any) => {
    setEditingPackage(pkg);
    setShowPackageForm(true);
  };

  const handleEditDress = (dress: any) => {
    setEditingDress(dress);
    setShowDressForm(true);
  };

  // Get dress rental status info
  const getDressRentalInfo = (dressId: string) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const activeBookings = bookings.filter(booking => 
      booking.status !== 'cancelled' && 
      booking.dressIds.includes(dressId)
    );

    for (const booking of activeBookings) {
      if (booking.dressRentalPeriods) {
        const dressRentalPeriod = booking.dressRentalPeriods.find(period => period.dressId === dressId);
        if (dressRentalPeriod) {
          const startDate = new Date(dressRentalPeriod.startDate);
          const endDate = new Date(dressRentalPeriod.endDate);
          startDate.setHours(0, 0, 0, 0);
          endDate.setHours(23, 59, 59, 999);

          if (today >= startDate && today <= endDate) {
            return {
              isCurrentlyRented: true,
              customerName: booking.customerName,
              startDate: dressRentalPeriod.startDate,
              endDate: dressRentalPeriod.endDate
            };
          }
        }
      }
    }

    return { isCurrentlyRented: false };
  };

  const isRTL = settings?.language === 'ar' || settings?.rtlMode;

  const renderServicesTab = () => (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className="relative flex-1 max-w-md">
          <Search className={`absolute top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 ${isRTL ? 'right-3' : 'left-3'}`} />
          <input
            type="text"
            placeholder={t.searchServices}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`${isRTL ? 'pr-10 pl-4 text-right' : 'pl-10 pr-4 text-left'} py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
          />
        </div>
        <button
          onClick={() => {
            setEditingService(null);
            setShowServiceForm(true);
          }}
          className={`flex items-center space-x-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
        >
          <Plus className="h-4 w-4" />
          <span>{t.addService}</span>
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.serviceName}
              </th>
              <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.duration}
              </th>
              <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.price}
              </th>
              <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.description}
              </th>
              <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                {t.actions}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredServices.map((service) => (
              <tr key={service.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}>
                  {service.name}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>
                  {service.duration} min
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}>
                  ${service.price}
                </td>
                <td className={`px-6 py-4 text-sm text-gray-500 dark:text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>
                  {service.description}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${isRTL ? 'space-x-reverse' : ''} space-x-2`}>
                  <button 
                    onClick={() => handleEditService(service)}
                    className={`text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 inline-flex items-center space-x-1 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
                  >
                    <Edit className="h-3 w-3" />
                    <span>{t.edit}</span>
                  </button>
                  <button 
                    onClick={() => handleDeleteService(service.id)}
                    className={`text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 inline-flex items-center space-x-1 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
                  >
                    <Trash2 className="h-3 w-3" />
                    <span>{t.delete}</span>
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredServices.length === 0 && (
        <div className="text-center py-12">
          <Scissors className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">{t.noServicesFound}</p>
        </div>
      )}
    </div>
  );

  const renderPackagesTab = () => (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t.servicePackages}</h3>
        <button
          onClick={() => {
            setEditingPackage(null);
            setShowPackageForm(true);
          }}
          className={`flex items-center space-x-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
        >
          <Plus className="h-4 w-4" />
          <span>{t.addPackage}</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {packages.map((pkg) => (
          <div key={pkg.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{pkg.name}</h4>
              <span className="text-xl font-bold text-gray-900 dark:text-white">${pkg.discountedPrice}</span>
            </div>
            <p className={`text-sm text-gray-600 dark:text-gray-400 mb-4 ${isRTL ? 'text-right' : 'text-left'}`}>{pkg.description}</p>
            
            <div className="space-y-2 mb-4">
              <h5 className={`font-medium text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}>{t.includedServices}:</h5>
              <div className={`space-y-1 text-sm text-gray-600 dark:text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>
                {pkg.services.map((serviceId) => {
                  const service = services.find(s => s.id === serviceId);
                  return service ? (
                    <div key={serviceId} className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span>{service.name}</span>
                      <span>{service.duration}min</span>
                    </div>
                  ) : null;
                })}
              </div>
            </div>
            
            <div className={`flex space-x-2 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
              <button 
                onClick={() => handleEditPackage(pkg)}
                className={`text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 text-sm inline-flex items-center space-x-1 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
              >
                <Edit className="h-3 w-3" />
                <span>{t.edit}</span>
              </button>
              <button 
                onClick={() => handleDeletePackage(pkg.id)}
                className={`text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 text-sm inline-flex items-center space-x-1 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
              >
                <Trash2 className="h-3 w-3" />
                <span>{t.delete}</span>
              </button>
            </div>
          </div>
        ))}
      </div>

      {packages.length === 0 && (
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">{t.noPackagesFound}</p>
        </div>
      )}
    </div>
  );

  const renderDressRentalsTab = () => (
    <div className="space-y-6">
      <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
        <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <div className="relative flex-1 max-w-md">
            <Search className={`absolute top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 ${isRTL ? 'right-3' : 'left-3'}`} />
            <input
              type="text"
              placeholder={t.searchDresses}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`${isRTL ? 'pr-10 pl-4 text-right' : 'pl-10 pr-4 text-left'} py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            />
          </div>
          <select 
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">{t.allStatus}</option>
            <option value="available">{t.available}</option>
            <option value="rented">{t.rented}</option>
            <option value="maintenance">{t.maintenance}</option>
          </select>
        </div>
        <button
          onClick={() => {
            setEditingDress(null);
            setShowDressForm(true);
          }}
          className={`flex items-center space-x-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
        >
          <Plus className="h-4 w-4" />
          <span>{t.addDress}</span>
        </button>
      </div>

      {/* Color Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h4 className={`font-medium text-gray-900 dark:text-white mb-3 ${isRTL ? 'text-right' : 'text-left'}`}>{t.colors}:</h4>
        <div className="flex flex-wrap gap-3">
          {colors.map((color) => (
            <label key={color.name} className={`flex items-center space-x-2 cursor-pointer ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
              <div className="relative">
                <input
                  type="checkbox"
                  checked={selectedColors.includes(color.name)}
                  onChange={() => handleColorToggle(color.name)}
                  className="sr-only"
                />
                <div 
                  className={`w-6 h-6 rounded-full border-2 transition-all duration-200 ${
                    selectedColors.includes(color.name) 
                      ? 'border-gray-900 dark:border-white shadow-lg scale-110' 
                      : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                  } ${color.name === 'White' ? 'border-gray-400' : ''}`}
                  style={{ 
                    backgroundColor: color.hex,
                    boxShadow: color.name === 'White' ? 'inset 0 0 0 1px rgba(0,0,0,0.1)' : undefined
                  }}
                >
                  {selectedColors.includes(color.name) && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className={`w-2 h-2 rounded-full ${
                        color.name === 'White' || color.name === 'Yellow' || color.name === 'Ivory' 
                          ? 'bg-gray-800' 
                          : 'bg-white'
                      }`}></div>
                    </div>
                  )}
                </div>
              </div>
              <span className="text-sm text-gray-700 dark:text-gray-300">{color.name}</span>
            </label>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {filteredDresses.map((dress) => {
          const rentalInfo = getDressRentalInfo(dress.id);
          
          return (
            <div key={dress.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="aspect-w-16 aspect-h-9 bg-gray-200 dark:bg-gray-700">
                <img 
                  src={dress.imageUrl || "https://images.pexels.com/photos/1021693/pexels-photo-1021693.jpeg?auto=compress&cs=tinysrgb&w=400"} 
                  alt={dress.name}
                  className="w-full h-48 object-cover"
                />
              </div>
              <div className="p-4">
                <div className={`flex items-center justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{dress.name}</h4>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    dress.status === 'available'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                      : dress.status === 'rented'
                      ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                  }`}>
                    {dress.status === 'available' ? t.available : dress.status === 'rented' ? t.rented : t.maintenance}
                  </span>
                </div>

                {/* Rental Status Info */}
                {rentalInfo.isCurrentlyRented && (
                  <div className="mb-3 p-2 bg-red-50 dark:bg-red-900/20 rounded-lg">
                    <p className={`text-sm text-red-800 dark:text-red-300 font-medium ${isRTL ? 'text-right' : 'text-left'}`}>
                      Currently rented to: {rentalInfo.customerName}
                    </p>
                    <p className={`text-xs text-red-600 dark:text-red-400 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {rentalInfo.startDate} to {rentalInfo.endDate}
                    </p>
                  </div>
                )}

                <p className={`text-sm text-gray-600 dark:text-gray-400 mb-3 ${isRTL ? 'text-right' : 'text-left'}`}>${dress.rentalPrice} {t.rentalPrice}</p>
                
                <div className="mb-3">
                  <h5 className={`font-medium text-gray-900 dark:text-white mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>{t.availableColors}:</h5>
                  <div className={`flex space-x-2 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    {dress.availableColors.map((colorName) => {
                      const colorInfo = colors.find(c => c.name === colorName);
                      return (
                        <div key={colorName} className="flex items-center space-x-1">
                          <div 
                            className={`w-4 h-4 rounded-full border ${
                              colorName === 'White' ? 'border-gray-400' : 'border-gray-300 dark:border-gray-600'
                            }`}
                            style={{ 
                              backgroundColor: colorInfo?.hex || '#6B7280',
                              boxShadow: colorName === 'White' ? 'inset 0 0 0 1px rgba(0,0,0,0.1)' : undefined
                            }}
                            title={colorName}
                          ></div>
                          <span className="text-xs text-gray-600 dark:text-gray-400">{colorName}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
                
                <p className={`text-sm text-gray-600 dark:text-gray-400 mb-4 ${isRTL ? 'text-right' : 'text-left'}`}>{dress.description}</p>
                
                <div className={`flex space-x-2 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  <button 
                    onClick={() => handleEditDress(dress)}
                    className={`text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 text-sm inline-flex items-center space-x-1 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
                  >
                    <Edit className="h-3 w-3" />
                    <span>{t.edit}</span>
                  </button>
                  <button 
                    onClick={() => handleDeleteDress(dress.id)}
                    className={`text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 text-sm inline-flex items-center space-x-1 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
                  >
                    <Trash2 className="h-3 w-3" />
                    <span>{t.delete}</span>
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredDresses.length === 0 && (
        <div className="text-center py-12">
          <Shirt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">{t.noDressesFound}</p>
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={isRTL ? 'text-right' : 'text-left'}>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t.serviceManagement}</h1>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className={`flex space-x-8 px-6 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
            {[
              { id: 'services', label: t.services, icon: Scissors },
              { id: 'packages', label: t.packages, icon: Package },
              { id: 'dresses', label: t.dressRentals, icon: Shirt },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`
                    flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors
                    ${activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                    }
                    ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}
                  `}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'services' && renderServicesTab()}
          {activeTab === 'packages' && renderPackagesTab()}
          {activeTab === 'dresses' && renderDressRentalsTab()}
        </div>
      </div>

      {/* Modals */}
      {showServiceForm && (
        <ServiceForm 
          service={editingService}
          onClose={() => {
            setShowServiceForm(false);
            setEditingService(null);
          }} 
        />
      )}
      {showPackageForm && (
        <PackageForm 
          package={editingPackage}
          onClose={() => {
            setShowPackageForm(false);
            setEditingPackage(null);
          }} 
        />
      )}
      {showDressForm && (
        <DressForm 
          dress={editingDress}
          onClose={() => {
            setShowDressForm(false);
            setEditingDress(null);
          }} 
        />
      )}
    </div>
  );
}