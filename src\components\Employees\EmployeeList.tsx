import React, { useState } from 'react';
import { Plus, Search, Users, TrendingUp, DollarSign, BarChart3, User, Calendar } from 'lucide-react';
import { Employee } from '../../types';
import { useEmployees, useAttendance, useAdvances, useSettings } from '../../hooks/useDatabase';
import { format } from 'date-fns';
import { useTranslation } from '../../utils/translations';
import EmployeeForm from './EmployeeForm';
import AdvancesTab from './AdvancesTab';

export default function EmployeeList() {
  const { employees, loading } = useEmployees();
  const { attendance } = useAttendance();
  const { advances } = useAdvances();
  const { settings } = useSettings(true);
  const t = useTranslation(settings?.language || 'en');
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'active' | 'leave' | 'advances'>('all');

  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    switch (activeTab) {
      case 'active':
        return matchesSearch && employee.status === 'active';
      case 'leave':
        return matchesSearch && employee.status === 'inactive';
      case 'all':
      default:
        return matchesSearch;
    }
  });

  const handleEdit = (employee: Employee) => {
    setEditingEmployee(employee);
    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingEmployee(null);
  };

  const getEmployeeAttendanceRate = (employeeId: string) => {
    const employeeAttendance = attendance.filter(record => record.employeeId === employeeId);
    const presentDays = employeeAttendance.filter(record => record.status === 'present').length;
    const totalDays = employeeAttendance.length;
    return totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0;
  };

  const getLastCheckIn = (employeeId: string) => {
    const employeeAttendance = attendance
      .filter(record => record.employeeId === employeeId && record.checkIn)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    
    if (employeeAttendance.length > 0) {
      const lastRecord = employeeAttendance[0];
      return `${lastRecord.date} ${lastRecord.checkIn}`;
    }
    return 'Never';
  };

  const activeEmployees = employees.filter(emp => emp.status === 'active');
  const totalPayroll = activeEmployees.reduce((sum, emp) => sum + emp.salary, 0);
  const avgAttendance = activeEmployees.length > 0 
    ? Math.round(activeEmployees.reduce((sum, emp) => sum + getEmployeeAttendanceRate(emp.id), 0) / activeEmployees.length)
    : 0;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (activeTab === 'advances') {
    return <AdvancesTab />;
  }

  const isRTL = settings?.language === 'ar' || settings?.rtlMode;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t.teamManagement}</h1>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className={`flex items-center space-x-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
        >
          <Plus className="h-4 w-4" />
          <span>{t.addEmployee}</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{employees.length}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{t.totalEmployees}</p>
              <p className="text-xs text-gray-500">+2 {t.fromLastMonth}</p>
            </div>
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{activeEmployees.length}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{t.activeEmployees}</p>
              <p className="text-xs text-gray-500">100% {t.ofTotal}</p>
            </div>
            <div className="p-2 bg-emerald-100 dark:bg-emerald-900 rounded-lg">
              <User className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{avgAttendance}%</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{t.avgAttendance}</p>
              <p className="text-xs text-gray-500">+3% {t.fromLastMonth}</p>
            </div>
            <div className="p-2 bg-amber-100 dark:bg-amber-900 rounded-lg">
              <BarChart3 className="h-6 w-6 text-amber-600 dark:text-amber-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">${totalPayroll.toLocaleString()}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">{t.totalPayroll}</p>
              <p className="text-xs text-gray-500">{t.monthlyTotal}</p>
            </div>
            <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <Search className={`absolute top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 ${isRTL ? 'right-3' : 'left-3'}`} />
            <input
              type="text"
              placeholder={`${t.employee}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`${isRTL ? 'pr-10 pl-4 text-right' : 'pl-10 pr-4 text-left'} py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            />
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className={`flex space-x-8 px-6 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
            {[
              { id: 'all', label: t.allEmployees, count: employees.length },
              { id: 'active', label: t.active, count: activeEmployees.length },
              { id: 'leave', label: t.onLeave, count: employees.filter(e => e.status === 'inactive').length },
              { id: 'advances', label: t.advances, count: null },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`
                  flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors
                  ${activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }
                  ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}
                `}
              >
                <span>{tab.label}</span>
                {tab.count !== null && (
                  <span className={`
                    px-2 py-1 rounded-full text-xs
                    ${activeTab === tab.id
                      ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                    }
                  `}>
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Employee Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.employee}
                </th>
                <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.position}
                </th>
                <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.status}
                </th>
                <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.attendanceRate}
                </th>
                <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.salary}
                </th>
                <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.lastCheckIn}
                </th>
                <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t.actions}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredEmployees.map((employee) => {
                const attendanceRate = getEmployeeAttendanceRate(employee.id);
                const lastCheckIn = getLastCheckIn(employee.id);
                
                return (
                  <tr key={employee.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse space-x-reverse' : ''} space-x-4`}>
                        <div className="flex-shrink-0 h-10 w-10">
                          {employee.photo ? (
                            <img 
                              src={employee.photo} 
                              alt={employee.name}
                              className="h-10 w-10 rounded-full object-cover"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                              <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">
                                {employee.name.split(' ').map(n => n[0]).join('')}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {employee.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {employee.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}>
                      {employee.position}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        employee.status === 'active'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                      }`}>
                        {employee.status === 'active' ? t.active : t.onLeave}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse space-x-reverse' : ''} space-x-2`}>
                        <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${attendanceRate}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-900 dark:text-white">{attendanceRate}%</span>
                      </div>
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}>
                      ${employee.salary.toLocaleString()}
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {lastCheckIn !== 'Never' ? format(new Date(lastCheckIn), 'yyyy-MM-dd HH:mm a') : 'Never'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => handleEdit(employee)}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        {t.viewProfile}
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {filteredEmployees.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400">{t.noEmployeesFound}</p>
          </div>
        )}
      </div>

      {/* Employee Form Modal */}
      {showForm && (
        <EmployeeForm
          employee={editingEmployee}
          onClose={handleCloseForm}
        />
      )}
    </div>
  );
}