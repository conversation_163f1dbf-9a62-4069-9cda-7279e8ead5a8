const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// --------- Expose some API to the Renderer process ---------
contextBridge.exposeInMainWorld('electronAPI', {
  // Database operations
  database: {
    // Generic CRUD operations
    getAll: (tableName: string) => ipcRenderer.invoke('db:getAll', tableName),
    get: (tableName: string, id: string) => ipcRenderer.invoke('db:get', tableName, id),
    add: (tableName: string, data: any) => ipcRenderer.invoke('db:add', tableName, data),
    update: (tableName: string, data: any) => ipcRenderer.invoke('db:update', tableName, data),
    delete: (tableName: string, id: string) => ipcRenderer.invoke('db:delete', tableName, id),
    getByIndex: (tableName: string, indexField: string, value: any) => 
      ipcRenderer.invoke('db:getByIndex', tableName, indexField, value),
    clearAllData: () => ipcRenderer.invoke('db:clearAllData'),
    
    // Migration operations
    migrateFromIndexedDB: (data: any) => ipcRenderer.invoke('db:migrateFromIndexedDB', data),
    exportData: () => ipcRenderer.invoke('db:exportData'),
    importData: (data: any) => ipcRenderer.invoke('db:importData', data),
  },

  // File system operations
  fs: {
    showSaveDialog: (options: any) => ipcRenderer.invoke('fs:showSaveDialog', options),
    showOpenDialog: (options: any) => ipcRenderer.invoke('fs:showOpenDialog', options),
    writeFile: (filePath: string, data: string) => ipcRenderer.invoke('fs:writeFile', filePath, data),
    readFile: (filePath: string) => ipcRenderer.invoke('fs:readFile', filePath),
  },

  // App information
  app: {
    getVersion: () => ipcRenderer.invoke('app:getVersion'),
    getPath: (name: string) => ipcRenderer.invoke('app:getPath', name),
    quit: () => ipcRenderer.invoke('app:quit'),
  },

  // Window operations
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    close: () => ipcRenderer.invoke('window:close'),
    isMaximized: () => ipcRenderer.invoke('window:isMaximized'),
  },

  // Event listeners
  on: (channel: string, callback: Function) => {
    ipcRenderer.on(channel, (event, ...args) => callback(...args));
  },
  
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },
});

// Types for TypeScript
export interface ElectronAPI {
  database: {
    getAll: (tableName: string) => Promise<any[]>;
    get: (tableName: string, id: string) => Promise<any>;
    add: (tableName: string, data: any) => Promise<void>;
    update: (tableName: string, data: any) => Promise<void>;
    delete: (tableName: string, id: string) => Promise<void>;
    getByIndex: (tableName: string, indexField: string, value: any) => Promise<any[]>;
    clearAllData: () => Promise<void>;
    migrateFromIndexedDB: (data: any) => Promise<void>;
    exportData: () => Promise<any>;
    importData: (data: any) => Promise<void>;
  };
  fs: {
    showSaveDialog: (options: any) => Promise<any>;
    showOpenDialog: (options: any) => Promise<any>;
    writeFile: (filePath: string, data: string) => Promise<void>;
    readFile: (filePath: string) => Promise<string>;
  };
  app: {
    getVersion: () => Promise<string>;
    getPath: (name: string) => Promise<string>;
    quit: () => Promise<void>;
  };
  window: {
    minimize: () => Promise<void>;
    maximize: () => Promise<void>;
    close: () => Promise<void>;
    isMaximized: () => Promise<boolean>;
  };
  on: (channel: string, callback: Function) => void;
  removeAllListeners: (channel: string) => void;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
