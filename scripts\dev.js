#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Development script for SalonSys Electron app
console.log('🚀 Starting SalonSys Development Environment...\n');

// Build the Electron main process first
console.log('📦 Building Electron main process...');
const buildElectron = spawn('npm', ['run', 'build:electron'], {
  stdio: 'inherit',
  shell: true,
  cwd: path.resolve(__dirname, '..')
});

buildElectron.on('close', (code) => {
  if (code !== 0) {
    console.error('❌ Failed to build Electron main process');
    process.exit(1);
  }
  
  console.log('✅ Electron main process built successfully\n');
  console.log('🔄 Starting development servers...\n');
  
  // Start the development server and Electron app concurrently
  const dev = spawn('npm', ['run', 'electron:dev'], {
    stdio: 'inherit',
    shell: true,
    cwd: path.resolve(__dirname, '..')
  });
  
  dev.on('close', (code) => {
    console.log(`\n🛑 Development server stopped with code ${code}`);
  });
  
  // Handle Ctrl+C gracefully
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping development environment...');
    dev.kill('SIGINT');
    process.exit(0);
  });
});

buildElectron.on('error', (err) => {
  console.error('❌ Error starting build process:', err);
  process.exit(1);
});
