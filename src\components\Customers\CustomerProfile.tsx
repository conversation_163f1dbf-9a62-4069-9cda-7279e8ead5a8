import React, { useState } from 'react';
import { X, User, Calendar, DollarSign, ExternalLink, MessageCircle } from 'lucide-react';
import { Customer } from '../../types';
import { useCustomers, useBookings, useServices, useServicePackages, useDressRentals } from '../../hooks/useDatabase';
import { format } from 'date-fns';

interface CustomerProfileProps {
  customer: Customer;
  onClose: () => void;
}

export default function CustomerProfile({ customer, onClose }: CustomerProfileProps) {
  const { updateCustomer } = useCustomers();
  const { bookings } = useBookings();
  const { services } = useServices();
  const { packages } = useServicePackages();
  const { dresses } = useDressRentals();
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState<'upcoming' | 'history'>('upcoming');
  const [formData, setFormData] = useState({
    name: customer.name,
    phone: customer.phone,
    email: customer.email,
    address: customer.address || '',
    notes: customer.notes || '',
    instagram: customer.instagram || '',
    facebook: customer.facebook || '',
    whatsapp: customer.whatsapp || '',
  });

  // Get customer bookings
  const customerBookings = bookings.filter(booking => 
    booking.customerId === customer.id || booking.customerName === customer.name
  );

  const today = new Date();
  const todayString = format(today, 'yyyy-MM-dd');

  const upcomingBookings = customerBookings.filter(booking => 
    booking.date >= todayString && (booking.status === 'confirmed' || booking.status === 'pending')
  );

  const pastBookings = customerBookings.filter(booking => 
    booking.date < todayString || booking.status === 'completed' || booking.status === 'cancelled'
  );

  const handleSave = async () => {
    await updateCustomer(customer.id, formData);
    setIsEditing(false);
  };

  const getServiceName = (serviceId: string) => {
    const service = services.find(s => s.id === serviceId);
    return service ? service.name : 'Unknown Service';
  };

  const getPackageName = (packageId: string) => {
    const pkg = packages.find(p => p.id === packageId);
    return pkg ? pkg.name : 'Unknown Package';
  };

  const getDressName = (dressId: string) => {
    const dress = dresses.find(d => d.id === dressId);
    return dress ? dress.name : 'Unknown Dress';
  };

  const getBookingServices = (booking: any) => {
    const serviceNames = booking.serviceIds.map(getServiceName);
    const packageNames = booking.packageIds.map(getPackageName);
    const dressNames = booking.dressIds.map(getDressName);
    
    return [...serviceNames, ...packageNames, ...dressNames].join(', ') || 'No services specified';
  };

  const formatSocialMediaLink = (platform: string, username: string) => {
    if (!username) return null;
    
    switch (platform) {
      case 'instagram':
        const instagramUsername = username.startsWith('@') ? username.slice(1) : username;
        return `https://instagram.com/${instagramUsername}`;
      case 'facebook':
        const facebookUsername = username.includes('facebook.com/') ? username : `facebook.com/${username}`;
        return facebookUsername.startsWith('http') ? facebookUsername : `https://${facebookUsername}`;
      case 'whatsapp':
        const phoneNumber = username.replace(/[^\d+]/g, '');
        return `https://wa.me/${phoneNumber}`;
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Customer Profile</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            View and edit customer information and booking history
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Basic Information */}
            <div className="lg:col-span-2">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span>Basic Information</span>
                </h3>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    readOnly={!isEditing}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white ${
                      isEditing ? 'bg-white dark:bg-gray-700' : 'bg-gray-50 dark:bg-gray-600'
                    }`}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Phone
                    </label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                      readOnly={!isEditing}
                      className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white ${
                        isEditing ? 'bg-white dark:bg-gray-700' : 'bg-gray-50 dark:bg-gray-600'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      readOnly={!isEditing}
                      className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white ${
                        isEditing ? 'bg-white dark:bg-gray-700' : 'bg-gray-50 dark:bg-gray-600'
                      }`}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Address
                  </label>
                  <input
                    type="text"
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    readOnly={!isEditing}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white ${
                      isEditing ? 'bg-white dark:bg-gray-700' : 'bg-gray-50 dark:bg-gray-600'
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Notes
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    readOnly={!isEditing}
                    rows={3}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white ${
                      isEditing ? 'bg-white dark:bg-gray-700' : 'bg-gray-50 dark:bg-gray-600'
                    }`}
                  />
                </div>
              </div>
            </div>

            {/* Customer Stats */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customer Stats</h3>
              
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Total Bookings:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{customerBookings.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Total Spent:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    ${customerBookings.reduce((sum, booking) => sum + booking.totalAmount, 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Last Visit:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {pastBookings.length > 0 
                      ? format(new Date(pastBookings.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0].date), 'M/d/yyyy')
                      : 'Never'
                    }
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Customer Since:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {format(new Date(customer.customerSince), 'M/d/yyyy')}
                  </span>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Favorite Services:</h4>
                <div className="space-y-1">
                  {customer.favoriteServices.length > 0 ? (
                    customer.favoriteServices.map((service, index) => (
                      <span key={index} className="inline-block px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 text-xs rounded mr-2 mb-1">
                        {service}
                      </span>
                    ))
                  ) : (
                    <span className="inline-block px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 text-xs rounded mr-2">
                      Hair Styling
                    </span>
                  )}
                </div>
              </div>

              {/* Social Media */}
              <div className="mt-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Social Media</h4>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">Instagram</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={formData.instagram}
                        onChange={(e) => setFormData(prev => ({ ...prev, instagram: e.target.value }))}
                        readOnly={!isEditing}
                        placeholder="@username"
                        className={`flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white text-sm ${
                          isEditing ? 'bg-white dark:bg-gray-700' : 'bg-gray-50 dark:bg-gray-600'
                        }`}
                      />
                      {formData.instagram && (
                        <a
                          href={formatSocialMediaLink('instagram', formData.instagram) || '#'}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">Facebook</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={formData.facebook}
                        onChange={(e) => setFormData(prev => ({ ...prev, facebook: e.target.value }))}
                        readOnly={!isEditing}
                        placeholder="facebook.com/username"
                        className={`flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white text-sm ${
                          isEditing ? 'bg-white dark:bg-gray-700' : 'bg-gray-50 dark:bg-gray-600'
                        }`}
                      />
                      {formData.facebook && (
                        <a
                          href={formatSocialMediaLink('facebook', formData.facebook) || '#'}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">WhatsApp</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={formData.whatsapp}
                        onChange={(e) => setFormData(prev => ({ ...prev, whatsapp: e.target.value }))}
                        readOnly={!isEditing}
                        placeholder="+1234567890"
                        className={`flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white text-sm ${
                          isEditing ? 'bg-white dark:bg-gray-700' : 'bg-gray-50 dark:bg-gray-600'
                        }`}
                      />
                      {formData.whatsapp && (
                        <a
                          href={formatSocialMediaLink('whatsapp', formData.whatsapp) || '#'}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 transition-colors"
                        >
                          <MessageCircle className="h-4 w-4" />
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Booking History */}
          <div className="mt-8 border-t border-gray-200 dark:border-gray-700 pt-6">
            <div className="flex space-x-8 mb-6">
              <button
                onClick={() => setActiveTab('upcoming')}
                className={`pb-2 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'upcoming'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                Upcoming Bookings ({upcomingBookings.length})
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`pb-2 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'history'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                History ({pastBookings.length})
              </button>
            </div>

            {activeTab === 'upcoming' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span>Upcoming Bookings</span>
                </h3>
                
                <div className="space-y-4">
                  {upcomingBookings.length > 0 ? (
                    upcomingBookings.map((booking) => (
                      <div key={booking.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <Calendar className="h-4 w-4 text-gray-500" />
                              <span className="font-medium text-gray-900 dark:text-white">
                                {booking.title || getBookingServices(booking)}
                              </span>
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                booking.status === 'confirmed'
                                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                                  : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                              }`}>
                                {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {format(new Date(booking.date), 'MMMM d, yyyy')} at {booking.startTime}
                            </p>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">${booking.totalAmount}</p>
                            {booking.notes && (
                              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{booking.notes}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 dark:text-gray-400">No upcoming bookings</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'history' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span>Booking History</span>
                </h3>
                
                <div className="space-y-4">
                  {pastBookings.length > 0 ? (
                    pastBookings
                      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                      .map((booking) => (
                        <div key={booking.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <Calendar className="h-4 w-4 text-gray-500" />
                                <span className="font-medium text-gray-900 dark:text-white">
                                  {booking.title || getBookingServices(booking)}
                                </span>
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  booking.status === 'completed'
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                }`}>
                                  {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                                </span>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {format(new Date(booking.date), 'MMMM d, yyyy')} at {booking.startTime}
                              </p>
                              <p className="text-sm font-medium text-gray-900 dark:text-white">${booking.totalAmount}</p>
                              {booking.notes && (
                                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{booking.notes}</p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))
                  ) : (
                    <div className="text-center py-8">
                      <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 dark:text-gray-400">No booking history found</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700 mt-6">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
            {isEditing ? (
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Save Changes
              </button>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors"
              >
                Edit Profile
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}