# Bolt ignore file - excludes files from being sent to the AI

# Dependencies
node_modules/
package-lock.json

# Build outputs
dist/
build/
.next/
.nuxt/
.output/

# Environment files
.env
.env.local
.env.production
.env.staging

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Cache directories
.cache/
.parcel-cache/
.eslintcache

# Testing
coverage/
.nyc_output/

# Temporary files
*.tmp
*.temp

# Large media files
*.mp4
*.avi
*.mov
*.mkv
*.jpg
*.jpeg
*.png
*.gif
*.svg
*.ico
*.webp

# Documentation that doesn't need AI context
README.md
CHANGELOG.md
LICENSE
*.md

# Config files that rarely change
.gitignore
.eslintrc*
.prettierrc*
tsconfig.json
tsconfig.*.json
vite.config.ts
tailwind.config.js
postcss.config.js