@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Font Family Configuration */
:root {
  --font-latin: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  --font-arabic: '<PERSON><PERSON>wal', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

/* Default font for LTR */
body {
  font-family: var(--font-latin);
  font-feature-settings: 'kern' 1, 'liga' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Arabic font for RTL */
[dir="rtl"], 
[lang="ar"],
.rtl {
  font-family: var(--font-arabic);
  font-feature-settings: 'kern' 1;
}

/* Improve Arabic text rendering */
[dir="rtl"] *,
[lang="ar"] *,
.rtl * {
  font-family: var(--font-arabic);
}

/* Primary Color CSS Custom Properties */
:root {
  /* Default blue colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  
  /* Main primary color variables */
  --primary-color: #2563eb;
  --primary-color-hover: #1d4ed8;
  --primary-color-light: #dbeafe;
  --primary-color-dark: #1e40af;
}

/* Custom utility classes for primary colors */
.bg-primary-50 { background-color: var(--primary-50); }
.bg-primary-100 { background-color: var(--primary-100); }
.bg-primary-200 { background-color: var(--primary-200); }
.bg-primary-300 { background-color: var(--primary-300); }
.bg-primary-400 { background-color: var(--primary-400); }
.bg-primary-500 { background-color: var(--primary-500); }
.bg-primary-600 { background-color: var(--primary-600); }
.bg-primary-700 { background-color: var(--primary-700); }
.bg-primary-800 { background-color: var(--primary-800); }
.bg-primary-900 { background-color: var(--primary-900); }

.text-primary-50 { color: var(--primary-50); }
.text-primary-100 { color: var(--primary-100); }
.text-primary-200 { color: var(--primary-200); }
.text-primary-300 { color: var(--primary-300); }
.text-primary-400 { color: var(--primary-400); }
.text-primary-500 { color: var(--primary-500); }
.text-primary-600 { color: var(--primary-600); }
.text-primary-700 { color: var(--primary-700); }
.text-primary-800 { color: var(--primary-800); }
.text-primary-900 { color: var(--primary-900); }

.border-primary-50 { border-color: var(--primary-50); }
.border-primary-100 { border-color: var(--primary-100); }
.border-primary-200 { border-color: var(--primary-200); }
.border-primary-300 { border-color: var(--primary-300); }
.border-primary-400 { border-color: var(--primary-400); }
.border-primary-500 { border-color: var(--primary-500); }
.border-primary-600 { border-color: var(--primary-600); }
.border-primary-700 { border-color: var(--primary-700); }
.border-primary-800 { border-color: var(--primary-800); }
.border-primary-900 { border-color: var(--primary-900); }

/* Hover states */
.hover\:bg-primary-50:hover { background-color: var(--primary-50); }
.hover\:bg-primary-100:hover { background-color: var(--primary-100); }
.hover\:bg-primary-200:hover { background-color: var(--primary-200); }
.hover\:bg-primary-300:hover { background-color: var(--primary-300); }
.hover\:bg-primary-400:hover { background-color: var(--primary-400); }
.hover\:bg-primary-500:hover { background-color: var(--primary-500); }
.hover\:bg-primary-600:hover { background-color: var(--primary-600); }
.hover\:bg-primary-700:hover { background-color: var(--primary-700); }
.hover\:bg-primary-800:hover { background-color: var(--primary-800); }
.hover\:bg-primary-900:hover { background-color: var(--primary-900); }

.hover\:text-primary-50:hover { color: var(--primary-50); }
.hover\:text-primary-100:hover { color: var(--primary-100); }
.hover\:text-primary-200:hover { color: var(--primary-200); }
.hover\:text-primary-300:hover { color: var(--primary-300); }
.hover\:text-primary-400:hover { color: var(--primary-400); }
.hover\:text-primary-500:hover { color: var(--primary-500); }
.hover\:text-primary-600:hover { color: var(--primary-600); }
.hover\:text-primary-700:hover { color: var(--primary-700); }
.hover\:text-primary-800:hover { color: var(--primary-800); }
.hover\:text-primary-900:hover { color: var(--primary-900); }

/* Focus states */
.focus\:ring-primary-500:focus { 
  --tw-ring-color: var(--primary-500);
}

.focus\:border-primary-500:focus { 
  border-color: var(--primary-500);
}

/* Dark mode variants */
.dark .dark\:bg-primary-50 { background-color: var(--primary-50); }
.dark .dark\:bg-primary-100 { background-color: var(--primary-100); }
.dark .dark\:bg-primary-200 { background-color: var(--primary-200); }
.dark .dark\:bg-primary-300 { background-color: var(--primary-300); }
.dark .dark\:bg-primary-400 { background-color: var(--primary-400); }
.dark .dark\:bg-primary-500 { background-color: var(--primary-500); }
.dark .dark\:bg-primary-600 { background-color: var(--primary-600); }
.dark .dark\:bg-primary-700 { background-color: var(--primary-700); }
.dark .dark\:bg-primary-800 { background-color: var(--primary-800); }
.dark .dark\:bg-primary-900 { background-color: var(--primary-900); }

.dark .dark\:text-primary-50 { color: var(--primary-50); }
.dark .dark\:text-primary-100 { color: var(--primary-100); }
.dark .dark\:text-primary-200 { color: var(--primary-200); }
.dark .dark\:text-primary-300 { color: var(--primary-300); }
.dark .dark\:text-primary-400 { color: var(--primary-400); }
.dark .dark\:text-primary-500 { color: var(--primary-500); }
.dark .dark\:text-primary-600 { color: var(--primary-600); }
.dark .dark\:text-primary-700 { color: var(--primary-700); }
.dark .dark\:text-primary-800 { color: var(--primary-800); }
.dark .dark\:text-primary-900 { color: var(--primary-900); }