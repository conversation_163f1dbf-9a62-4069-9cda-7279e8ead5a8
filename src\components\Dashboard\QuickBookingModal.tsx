import React, { useState, useCallback, useMemo } from 'react';
import { X, Calendar, Clock, DollarSign } from 'lucide-react';
import { format } from 'date-fns';
import { useServices, useServicePackages, useBookings, useCustomers } from '../../hooks/useDatabase';

interface QuickBookingModalProps {
  onClose: () => void;
}

export default function QuickBookingModal({ onClose }: QuickBookingModalProps) {
  const { services } = useServices();
  const { packages } = useServicePackages();
  const { addBooking } = useBookings();
  const { customers, addCustomer } = useCustomers();
  const [formData, setFormData] = useState({
    customerName: '',
    serviceType: 'service' as 'service' | 'package',
    selectedService: '',
    selectedPackage: '',
    date: format(new Date(), 'yyyy-MM-dd'),
    startTime: '',
    notes: '',
    discountPercentage: 0,
  });

  // Customer search state
  const [customerSearchTerm, setCustomerSearchTerm] = useState('');
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  const [filteredCustomers, setFilteredCustomers] = useState(customers);

  // Filter customers based on search term
  React.useEffect(() => {
    if (customerSearchTerm.trim() === '') {
      setFilteredCustomers([]);
      setShowCustomerDropdown(false);
    } else {
      const filtered = customers.filter(customer =>
        customer.name.toLowerCase().includes(customerSearchTerm.toLowerCase()) ||
        customer.phone.includes(customerSearchTerm) ||
        customer.email.toLowerCase().includes(customerSearchTerm.toLowerCase())
      );
      setFilteredCustomers(filtered);
      setShowCustomerDropdown(true);
    }
  }, [customerSearchTerm, customers]);

  const handleCustomerSearch = (value: string) => {
    setCustomerSearchTerm(value);
    setFormData(prev => ({ ...prev, customerName: value }));
  };

  const handleCustomerSelect = (customer: any) => {
    setCustomerSearchTerm(customer.name);
    setFormData(prev => ({ ...prev, customerName: customer.name }));
    setShowCustomerDropdown(false);
  };

  const handleAddNewCustomer = async () => {
    if (!customerSearchTerm.trim()) return;

    try {
      // Create new customer with the entered name
      await addCustomer({
        name: customerSearchTerm.trim(),
        phone: '',
        email: '',
        address: '',
        notes: `Added from quick booking on ${format(new Date(), 'yyyy-MM-dd')}`,
        instagram: '',
        facebook: '',
        whatsapp: '',
        totalBookings: 0,
        totalSpent: 0,
        customerSince: new Date().toISOString(),
        favoriteServices: [],
      });

      // Update form data and close dropdown
      setFormData(prev => ({ ...prev, customerName: customerSearchTerm.trim() }));
      setShowCustomerDropdown(false);
      
      // Show success message
      alert(`Customer "${customerSearchTerm.trim()}" has been added to your customer list!`);
    } catch (error) {
      console.error('Error adding new customer:', error);
      alert('Failed to add new customer. Please try again.');
    }
  };

  // Calculate pricing
  const calculateSubtotal = useCallback(() => {
    if (formData.serviceType === 'service' && formData.selectedService) {
      const service = services.find(s => s.id === formData.selectedService);
      return service ? service.price : 0;
    } else if (formData.serviceType === 'package' && formData.selectedPackage) {
      const pkg = packages.find(p => p.id === formData.selectedPackage);
      return pkg ? pkg.discountedPrice : 0;
    }
    return 0;
  }, [formData.serviceType, formData.selectedService, formData.selectedPackage, services, packages]);

  const subtotal = calculateSubtotal();
  const discount = (subtotal * formData.discountPercentage) / 100;
  const totalAmount = subtotal - discount;

  const handleQuickDiscount = (percentage: number) => {
    setFormData(prev => ({ ...prev, discountPercentage: percentage }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const bookingData = {
      customerId: undefined,
      customerName: formData.customerName,
      title: formData.serviceType === 'service' 
        ? services.find(s => s.id === formData.selectedService)?.name
        : packages.find(p => p.id === formData.selectedPackage)?.name,
      date: formData.date,
      startTime: formData.startTime,
      endTime: '', // Will be calculated based on service duration
      employeeIds: [],
      serviceIds: formData.serviceType === 'service' && formData.selectedService ? [formData.selectedService] : [],
      packageIds: formData.serviceType === 'package' && formData.selectedPackage ? [formData.selectedPackage] : [],
      dressIds: [],
      subtotal,
      discount,
      deposit: 0,
      totalAmount,
      amountDue: totalAmount,
      status: 'confirmed' as const,
      notes: formData.notes,
    };
    
    await addBooking(bookingData);
    onClose();
  };

  const activeServices = services.filter(service => service.isActive);
  const activePackages = packages.filter(pkg => pkg.isActive);

  // Check if customer name matches any existing customer exactly
  const exactCustomerMatch = customers.find(customer => 
    customer.name.toLowerCase() === customerSearchTerm.toLowerCase()
  );

  // Show "Add as new customer" option when there's text and no exact match
  const showAddNewCustomerOption = customerSearchTerm.trim() && !exactCustomerMatch && showCustomerDropdown;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Quick New Booking</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Create a booking quickly with minimal required information
          </p>

          {/* Date Field - Required */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Date *
            </label>
            <div className="relative">
              <input
                type="date"
                value={formData.date}
                onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                required
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
              <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
          </div>

          {/* Customer Search - Optional */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Customer Name (Optional)
            </label>
            <input
              type="text"
              value={customerSearchTerm}
              onChange={(e) => handleCustomerSearch(e.target.value)}
              placeholder="Search for existing customer or enter new name"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              onFocus={() => customerSearchTerm && setShowCustomerDropdown(true)}
              onBlur={() => setTimeout(() => setShowCustomerDropdown(false), 200)}
            />
            
            {/* Customer Dropdown */}
            {showCustomerDropdown && (
              <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                {/* Existing customers */}
                {filteredCustomers.map((customer) => (
                  <div
                    key={customer.id}
                    onClick={() => handleCustomerSelect(customer)}
                    className="p-3 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                  >
                    <div className="font-medium text-gray-900 dark:text-white">{customer.name}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">{customer.phone}</div>
                  </div>
                ))}
                
                {/* Add new customer option */}
                {showAddNewCustomerOption && (
                  <div
                    onClick={handleAddNewCustomer}
                    className="p-3 hover:bg-blue-50 dark:hover:bg-blue-900/20 cursor-pointer border-t border-gray-200 dark:border-gray-600 text-blue-600 dark:text-blue-400"
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">+</span>
                      <span>Add "{customerSearchTerm}" as new customer</span>
                    </div>
                    <div className="text-xs text-blue-500 dark:text-blue-400 mt-1">
                      This will save the customer to your customer list
                    </div>
                  </div>
                )}

                {/* No results message */}
                {filteredCustomers.length === 0 && !showAddNewCustomerOption && customerSearchTerm.trim() && (
                  <div className="p-3 text-gray-500 dark:text-gray-400 text-center">
                    No customers found
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Service Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Service Type (Optional)
            </label>
            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, serviceType: 'service', selectedPackage: '' }))}
                className={`p-3 border-2 rounded-lg transition-all duration-200 ${
                  formData.serviceType === 'service'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 text-gray-700 dark:text-gray-300'
                }`}
              >
                <div className="text-center">
                  <div className="font-medium">Service</div>
                  <div className="text-xs opacity-75">Individual service</div>
                </div>
              </button>
              <button
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, serviceType: 'package', selectedService: '' }))}
                className={`p-3 border-2 rounded-lg transition-all duration-200 ${
                  formData.serviceType === 'package'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 text-gray-700 dark:text-gray-300'
                }`}
              >
                <div className="text-center">
                  <div className="font-medium">Package</div>
                  <div className="text-xs opacity-75">Service bundle</div>
                </div>
              </button>
            </div>
          </div>

          {/* Service/Package Selection */}
          {formData.serviceType === 'service' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Select Service (Optional)
              </label>
              <select
                value={formData.selectedService}
                onChange={(e) => setFormData(prev => ({ ...prev, selectedService: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Select a service</option>
                {activeServices.map(service => (
                  <option key={service.id} value={service.id}>
                    {service.name} - ${service.price}
                  </option>
                ))}
              </select>
            </div>
          )}

          {formData.serviceType === 'package' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Select Package (Optional)
              </label>
              <select
                value={formData.selectedPackage}
                onChange={(e) => setFormData(prev => ({ ...prev, selectedPackage: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Select a package</option>
                {activePackages.map(pkg => (
                  <option key={pkg.id} value={pkg.id}>
                    {pkg.name} - ${pkg.discountedPrice}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Start Time - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Start Time (Optional)
            </label>
            <div className="relative">
              <input
                type="time"
                value={formData.startTime}
                onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
              <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
          </div>

          {/* Pricing Section */}
          {(formData.selectedService || formData.selectedPackage) && (
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">Pricing</h3>
              
              {/* Discount Percentage */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Discount Percentage
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={formData.discountPercentage}
                  onChange={(e) => setFormData(prev => ({ ...prev, discountPercentage: parseFloat(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                
                {/* Quick Discount Options */}
                <div className="mt-2">
                  <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">Quick options:</p>
                  <div className="flex space-x-2">
                    {[5, 10, 15, 20].map(percentage => (
                      <button
                        key={percentage}
                        type="button"
                        onClick={() => handleQuickDiscount(percentage)}
                        className="px-2 py-1 text-xs bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 rounded hover:bg-primary-200 dark:hover:bg-primary-800 transition-colors"
                      >
                        {percentage}%
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Pricing Summary */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Subtotal:</span>
                    <span className="font-medium text-gray-900 dark:text-white">${subtotal.toFixed(2)}</span>
                  </div>
                  {formData.discountPercentage > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Discount ({formData.discountPercentage}%):</span>
                      <span className="font-medium text-red-600 dark:text-red-400">-${discount.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="border-t border-gray-200 dark:border-gray-600 pt-1">
                    <div className="flex justify-between text-base font-bold">
                      <span className="text-gray-900 dark:text-white">Total:</span>
                      <span className="text-gray-900 dark:text-white">${totalAmount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Notes - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Notes (Optional)
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              placeholder="Any additional notes..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              Create Quick Booking
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}