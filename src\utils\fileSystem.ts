// File System utilities for Electron environment

export class FileSystemManager {
  private static isElectron(): boolean {
    return typeof window !== 'undefined' && !!window.electronAPI;
  }

  // Export data to file
  static async exportToFile(data: any, defaultFileName: string = 'salonsys-export.json'): Promise<void> {
    if (!this.isElectron()) {
      // Fallback to browser download for web version
      this.downloadAsFile(data, defaultFileName);
      return;
    }

    try {
      const result = await window.electronAPI.fs.showSaveDialog({
        title: 'Export SalonSys Data',
        defaultPath: defaultFileName,
        filters: [
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if (!result.canceled && result.filePath) {
        const jsonData = JSON.stringify(data, null, 2);
        await window.electronAPI.fs.writeFile(result.filePath, jsonData);
        console.log('Data exported successfully to:', result.filePath);
      }
    } catch (error) {
      console.error('Failed to export data:', error);
      throw new Error('Failed to export data to file');
    }
  }

  // Import data from file
  static async importFromFile(): Promise<any> {
    if (!this.isElectron()) {
      throw new Error('File import is only available in the desktop version');
    }

    try {
      const result = await window.electronAPI.fs.showOpenDialog({
        title: 'Import SalonSys Data',
        filters: [
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        properties: ['openFile']
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        const fileContent = await window.electronAPI.fs.readFile(filePath);
        return JSON.parse(fileContent);
      }

      return null;
    } catch (error) {
      console.error('Failed to import data:', error);
      throw new Error('Failed to import data from file');
    }
  }

  // Backup database
  static async backupDatabase(): Promise<void> {
    if (!this.isElectron()) {
      throw new Error('Database backup is only available in the desktop version');
    }

    try {
      const data = await window.electronAPI.database.exportData();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `salonsys-backup-${timestamp}.json`;
      
      await this.exportToFile(data, fileName);
    } catch (error) {
      console.error('Failed to backup database:', error);
      throw new Error('Failed to backup database');
    }
  }

  // Restore database from backup
  static async restoreDatabase(): Promise<void> {
    if (!this.isElectron()) {
      throw new Error('Database restore is only available in the desktop version');
    }

    try {
      const data = await this.importFromFile();
      if (data) {
        await window.electronAPI.database.importData(data);
        console.log('Database restored successfully');
      }
    } catch (error) {
      console.error('Failed to restore database:', error);
      throw new Error('Failed to restore database');
    }
  }

  // Get application data directory
  static async getAppDataPath(): Promise<string> {
    if (!this.isElectron()) {
      return 'localStorage'; // Fallback for web version
    }

    try {
      return await window.electronAPI.app.getPath('userData');
    } catch (error) {
      console.error('Failed to get app data path:', error);
      return '';
    }
  }

  // Get application version
  static async getAppVersion(): Promise<string> {
    if (!this.isElectron()) {
      return '1.0.0'; // Fallback for web version
    }

    try {
      return await window.electronAPI.app.getVersion();
    } catch (error) {
      console.error('Failed to get app version:', error);
      return '1.0.0';
    }
  }

  // Browser fallback for file download
  private static downloadAsFile(data: any, fileName: string): void {
    const jsonData = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  // Check if running in Electron environment
  static isDesktopApp(): boolean {
    return this.isElectron();
  }

  // Window controls (Electron only)
  static async minimizeWindow(): Promise<void> {
    if (this.isElectron()) {
      await window.electronAPI.window.minimize();
    }
  }

  static async maximizeWindow(): Promise<void> {
    if (this.isElectron()) {
      await window.electronAPI.window.maximize();
    }
  }

  static async closeWindow(): Promise<void> {
    if (this.isElectron()) {
      await window.electronAPI.window.close();
    }
  }

  static async isWindowMaximized(): Promise<boolean> {
    if (this.isElectron()) {
      return await window.electronAPI.window.isMaximized();
    }
    return false;
  }

  static async quitApp(): Promise<void> {
    if (this.isElectron()) {
      await window.electronAPI.app.quit();
    }
  }
}
