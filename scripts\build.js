#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Production build script for SalonSys Electron app
console.log('🏗️  Building SalonSys for Production...\n');

// Clean previous builds
console.log('🧹 Cleaning previous builds...');
const distDirs = ['dist', 'dist-electron', 'dist-electron-pack'];
distDirs.forEach(dir => {
  const dirPath = path.resolve(__dirname, '..', dir);
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
    console.log(`   Removed ${dir}/`);
  }
});

console.log('✅ Cleanup completed\n');

// Build the application
console.log('📦 Building application...');
const build = spawn('npm', ['run', 'build'], {
  stdio: 'inherit',
  shell: true,
  cwd: path.resolve(__dirname, '..')
});

build.on('close', (code) => {
  if (code !== 0) {
    console.error('❌ Build failed');
    process.exit(1);
  }
  
  console.log('\n✅ Build completed successfully!');
  console.log('\n📋 Build Summary:');
  console.log('   📁 dist/ - Renderer process (React app)');
  console.log('   📁 dist-electron/ - Main process (Electron)');
  console.log('\n🚀 To run the built application:');
  console.log('   npm run electron');
  console.log('\n📦 To create distributable packages:');
  console.log('   npm run electron:pack');
});

build.on('error', (err) => {
  console.error('❌ Error during build:', err);
  process.exit(1);
});
