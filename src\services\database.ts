class SalonDatabase {
  private db: IDBDatabase | null = null;
  private readonly dbName = 'SalonSysDB';
  private readonly version = 8; // Increased version to handle new dress rental periods

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Clear all existing stores to prevent data corruption
        const storeNames = ['employees', 'attendance', 'advances', 'payroll', 'services', 'packages', 'dresses', 'customers', 'bookings', 'settings', 'holidays', 'dayoffs'];
        
        storeNames.forEach(storeName => {
          if (db.objectStoreNames.contains(storeName)) {
            db.deleteObjectStore(storeName);
          }
        });

        // Recreate all stores
        // Employees store
        const employeeStore = db.createObjectStore('employees', { keyPath: 'id' });
        employeeStore.createIndex('name', 'name', { unique: false });
        employeeStore.createIndex('position', 'position', { unique: false });
        employeeStore.createIndex('status', 'status', { unique: false });

        // Attendance store
        const attendanceStore = db.createObjectStore('attendance', { keyPath: 'id' });
        attendanceStore.createIndex('employeeId', 'employeeId', { unique: false });
        attendanceStore.createIndex('date', 'date', { unique: false });

        // Employee Advances store
        const advancesStore = db.createObjectStore('advances', { keyPath: 'id' });
        advancesStore.createIndex('employeeId', 'employeeId', { unique: false });
        advancesStore.createIndex('date', 'date', { unique: false });
        advancesStore.createIndex('status', 'status', { unique: false });

        // Payroll store
        const payrollStore = db.createObjectStore('payroll', { keyPath: 'id' });
        payrollStore.createIndex('employeeId', 'employeeId', { unique: false });
        payrollStore.createIndex('month', 'month', { unique: false });
        payrollStore.createIndex('year', 'year', { unique: false });

        // Services store
        const servicesStore = db.createObjectStore('services', { keyPath: 'id' });
        servicesStore.createIndex('category', 'category', { unique: false });
        servicesStore.createIndex('isActive', 'isActive', { unique: false });

        // Service Packages store
        const packagesStore = db.createObjectStore('packages', { keyPath: 'id' });
        packagesStore.createIndex('isActive', 'isActive', { unique: false });

        // Dress Rentals store
        const dressesStore = db.createObjectStore('dresses', { keyPath: 'id' });
        dressesStore.createIndex('category', 'category', { unique: false });
        dressesStore.createIndex('status', 'status', { unique: false });

        // Customers store
        const customersStore = db.createObjectStore('customers', { keyPath: 'id' });
        customersStore.createIndex('name', 'name', { unique: false });
        customersStore.createIndex('phone', 'phone', { unique: false });
        customersStore.createIndex('email', 'email', { unique: false });

        // Bookings store (updated to support dress rental periods)
        const bookingsStore = db.createObjectStore('bookings', { keyPath: 'id' });
        bookingsStore.createIndex('customerId', 'customerId', { unique: false });
        bookingsStore.createIndex('date', 'date', { unique: false });
        bookingsStore.createIndex('status', 'status', { unique: false });

        // Settings store
        db.createObjectStore('settings', { keyPath: 'id' });

        // Holidays store
        const holidaysStore = db.createObjectStore('holidays', { keyPath: 'id' });
        holidaysStore.createIndex('date', 'date', { unique: false });
        holidaysStore.createIndex('type', 'type', { unique: false });

        // Employee Day-offs store
        const dayoffsStore = db.createObjectStore('dayoffs', { keyPath: 'id' });
        dayoffsStore.createIndex('employeeId', 'employeeId', { unique: false });
        dayoffsStore.createIndex('date', 'date', { unique: false });
        dayoffsStore.createIndex('status', 'status', { unique: false });
      };
    });
  }

  async getAll<T>(storeName: string): Promise<T[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
  }

  async get<T>(storeName: string, id: string): Promise<T | undefined> {
    if (!this.db) throw new Error('Database not initialized');
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(id);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
  }

  async add<T>(storeName: string, data: T): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.add(data);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async update<T>(storeName: string, data: T): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async delete(storeName: string, id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(id);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async getByIndex<T>(storeName: string, indexName: string, value: any): Promise<T[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const index = store.index(indexName);
      const request = index.getAll(value);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
  }

  // Method to clear all data (for development/testing)
  async clearAllData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    const storeNames = ['employees', 'attendance', 'advances', 'payroll', 'services', 'packages', 'dresses', 'customers', 'bookings', 'settings', 'holidays', 'dayoffs'];
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(storeNames, 'readwrite');
      
      let completed = 0;
      const total = storeNames.length;
      
      storeNames.forEach(storeName => {
        const store = transaction.objectStore(storeName);
        const request = store.clear();
        
        request.onsuccess = () => {
          completed++;
          if (completed === total) {
            resolve();
          }
        };
        
        request.onerror = () => reject(request.error);
      });
    });
  }
}

export const database = new SalonDatabase();