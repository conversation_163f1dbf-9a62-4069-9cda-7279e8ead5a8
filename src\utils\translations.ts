export interface Translations {
  // Sidebar
  salonManager: string;
  managementSystem: string;
  dashboard: string;
  teamManagement: string;
  serviceManagement: string;
  bookingManagement: string;
  financialDashboard: string;
  attendance: string;
  payroll: string;
  customers: string;
  settings: string;
  copyright: string;

  // Dashboard
  totalRevenue: string;
  fromCompletedBookingsOnly: string;
  totalBookings: string;
  completedAndConfirmed: string;
  activeEmployees: string;
  currentlyWorking: string;
  popularService: string;
  mostBookedService: string;
  workingHours: string;
  allowance: string;
  quickActions: string;
  quicklyPerformCommonTasks: string;
  quickNewBooking: string;
  addCustomer: string;
  viewCalendar: string;
  todaysAppointments: string;
  appointmentsToday: string;
  noAppointmentsScheduled: string;
  createNewBooking: string;
  revenueOverview: string;
  revenueTrends: string;
  weekly: string;
  monthly: string;
  yearly: string;
  daily: string;
  refresh: string;
  viewAll: string;
  appointments: string;

  // Team Management
  addEmployee: string;
  totalEmployees: string;
  avgAttendance: string;
  totalPayroll: string;
  monthlyTotal: string;
  allEmployees: string;
  active: string;
  onLeave: string;
  advances: string;
  employee: string;
  position: string;
  status: string;
  attendanceRate: string;
  salary: string;
  lastCheckIn: string;
  actions: string;
  viewProfile: string;
  noEmployeesFound: string;
  fromLastMonth: string;
  ofTotal: string;

  // Services Management
  services: string;
  packages: string;
  dressRentals: string;
  addService: string;
  addPackage: string;
  addDress: string;
  serviceName: string;
  duration: string;
  price: string;
  description: string;
  edit: string;
  delete: string;
  noServicesFound: string;
  servicePackages: string;
  noPackagesFound: string;
  includedServices: string;
  searchServices: string;
  searchDresses: string;
  allStatus: string;
  available: string;
  rented: string;
  maintenance: string;
  colors: string;
  availableColors: string;
  rentalPrice: string;
  noDressesFound: string;

  // Employee Advances
  requestAdvance: string;
  employeeAdvances: string;
  manageEmployeeSalaryAdvances: string;
  amount: string;
  reason: string;
  date: string;
  noAdvancesFound: string;
  currentMonthAdvancesSummary: string;
  advancesOverview: string;
  baseSalary: string;
  count: string;
  advancesCount: string;
  ofSalary: string;
  requestEmployeeAdvance: string;
  notAllowedDuringFirstWeek: string;
  selectEmployee: string;
  selectAnEmployee: string;
  advanceAmount: string;
  reasonForAdvance: string;
  enterReasonForAdvanceRequest: string;
  monthlySalary: string;
  advancePercentage: string;
  cancel: string;
  processAdvance: string;

  // Employee Forms
  addNewEmployee: string;
  editEmployee: string;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  hireDate: string;
  notes: string;
  changePhoto: string;
  remove: string;
  performanceStats: string;
  lastCheckInTime: string;
  employmentInfo: string;
  employeeId: string;
  attendanceAnalytics: string;
  lateMinutes: string;
  overtimeMinutes: string;
  absentDays: string;
  today: string;
  thisWeek: string;
  thisMonth: string;
  thisYear: string;
  previousWeek: string;
  previousMonth: string;
  previousYear: string;
  summary: string;
  totalLateMinutesThisMonth: string;
  totalOvertimeMinutesThisMonth: string;
  totalAbsentDaysThisMonth: string;
  latePenaltyThisMonth: string;
  overtimeBonusThisMonth: string;
  deleteEmployee: string;
  saveChanges: string;
  never: string;
  sessions: string;

  // Service Forms
  addNewService: string;
  editService: string;
  durationMinutes: string;
  category: string;
  hair: string;
  nail: string;
  facial: string;
  massage: string;
  other: string;
  updateService: string;

  // Package Forms
  addNewPackage: string;
  editPackage: string;
  packageName: string;
  selectServices: string;
  totalPrice: string;
  packagePrice: string;
  autoCalculated: string;
  selectedServices: string;
  totalValue: string;
  savings: string;
  updatePackage: string;

  // Dress Forms
  addNewDress: string;
  editDress: string;
  addDressDetailsIncludingPhotosAndColors: string;
  dressName: string;
  enterDressName: string;
  rentalPriceDollar: string;
  photos: string;
  addPhoto: string;
  enterDressDescription: string;
  saveDressChanges: string;

  // Booking Management
  newBooking: string;
  calendar: string;
  selectDateToViewBookings: string;
  bookingsFor: string;
  manageAppointmentsAndServices: string;
  bookings: string;
  noBookingsForThisDate: string;
  confirmed: string;
  completed: string;
  cancelled: string;
  pending: string;
  markCompleted: string;
  regularCustomer: string;
  prefersNaturalLook: string;

  // Financial Dashboard
  servicesRevenue: string;
  individualServicesOnly: string;
  packagesRevenue: string;
  dressRentalsRevenue: string;
  dressRentalIncome: string;
  revenueByCategory: string;
  breakdownOfRevenueSources: string;
  topPerformingEmployees: string;
  basedOnRevenueFromCompletedBookings: string;
  noPerformanceDataAvailable: string;
  monthlyRevenueTrend: string;
  revenueOverTimeFromCompletedBookings: string;

  // Attendance Management
  smartAttendanceManagement: string;
  isNotWorkingDay: string;
  holiday: string;
  employeesOnDayOff: string;
  exportReport: string;
  markAttendance: string;
  presentToday: string;
  outOfEmployees: string;
  lateArrivals: string;
  penaltyApplicable: string;
  absent: string;
  autoMarkedAfterAllowanceTime: string;
  overtime: string;
  extraHoursWorked: string;
  minAllowance: string;
  smartCalendar: string;
  viewAttendanceWithHolidaysAndDayOffs: string;
  nonWorkingDay: string;
  dayOff: string;
  quickActionsAttendance: string;
  bulkCheckIn: string;
  markManualPenalty: string;
  generateReport: string;
  smartAttendanceRecords: string;
  workingDay: string;
  nonWorkingDayParentheses: string;
  checkIn: string;
  checkOut: string;
  hours: string;
  lateOT: string;
  noActiveEmployeesFound: string;
  smartAttendanceRules: string;
  latePenalty: string;
  appliedProportionally: string;
  absencePenalty: string;
  excludesHolidaysDayOffs: string;
  overtimeBonus: string;
  after: string;
  smartFeatures: string;
  automaticallyExcludesHolidays: string;
  respectsIndividualEmployeeDayOffs: string;
  realTimeStatusCalculation: string;
  proportionalPenaltiesAndBonuses: string;
  visualCalendarWithIndicators: string;
  manualPenaltyApplication: string;
  autoAbsenceMarking: string;
  holidayDayOffWorkersNotLate: string;

  // Common
  inactive: string;
  paid: string;
  days: string;
  minutes: string;
  enterPlaceholder: string;
}

export const translations: Record<string, Translations> = {
  en: {
    // Sidebar
    salonManager: 'Salon Manager',
    managementSystem: 'Management System',
    dashboard: 'Dashboard',
    teamManagement: 'Team Management',
    serviceManagement: 'Service Management',
    bookingManagement: 'Booking Management',
    financialDashboard: 'Financial Dashboard',
    attendance: 'Attendance',
    payroll: 'Payroll',
    customers: 'Customers',
    settings: 'Settings',
    copyright: '© 2025 SalonSys v1.0',

    // Dashboard
    totalRevenue: 'Total Revenue',
    fromCompletedBookingsOnly: 'From completed bookings only',
    totalBookings: 'Total Bookings',
    completedAndConfirmed: 'Completed and confirmed',
    activeEmployees: 'Active Employees',
    currentlyWorking: 'Currently working',
    popularService: 'Popular Service',
    mostBookedService: 'Most booked service',
    workingHours: 'Working Hours',
    allowance: 'allowance',
    quickActions: 'Quick Actions',
    quicklyPerformCommonTasks: 'Quickly perform common tasks',
    quickNewBooking: 'Quick New Booking',
    addCustomer: 'Add Customer',
    viewCalendar: 'View Calendar',
    todaysAppointments: "Today's Appointments",
    appointmentsToday: 'appointments today',
    noAppointmentsScheduled: 'No appointments scheduled for today',
    createNewBooking: 'Create New Booking',
    revenueOverview: 'Revenue Overview',
    revenueTrends: 'Revenue trends from completed bookings only',
    weekly: 'Weekly',
    monthly: 'Monthly',
    yearly: 'Yearly',
    daily: 'Daily',
    refresh: 'Refresh',
    viewAll: 'View all',
    appointments: 'appointments',

    // Team Management
    addEmployee: 'Add Employee',
    totalEmployees: 'Total Employees',
    avgAttendance: 'Avg Attendance',
    totalPayroll: 'Total Payroll',
    monthlyTotal: 'Monthly total',
    allEmployees: 'All Employees',
    active: 'Active',
    onLeave: 'On Leave',
    advances: 'Advances',
    employee: 'Employee',
    position: 'Position',
    status: 'Status',
    attendanceRate: 'Attendance',
    salary: 'Salary',
    lastCheckIn: 'Last Check-in',
    actions: 'Actions',
    viewProfile: 'View Profile',
    noEmployeesFound: 'No employees found',
    fromLastMonth: 'from last month',
    ofTotal: 'of total',

    // Services Management
    services: 'Services',
    packages: 'Packages',
    dressRentals: 'Dress Rentals',
    addService: 'Add Service',
    addPackage: 'Add Package',
    addDress: 'Add Dress',
    serviceName: 'Service Name',
    duration: 'Duration',
    price: 'Price',
    description: 'Description',
    edit: 'Edit',
    delete: 'Delete',
    noServicesFound: 'No services found',
    servicePackages: 'Service Packages',
    noPackagesFound: 'No packages found',
    includedServices: 'Included Services',
    searchServices: 'Search services...',
    searchDresses: 'Search dresses...',
    allStatus: 'All Status',
    available: 'Available',
    rented: 'Rented',
    maintenance: 'Maintenance',
    colors: 'Colors',
    availableColors: 'Available Colors',
    rentalPrice: 'rental price',
    noDressesFound: 'No dresses found',

    // Employee Advances
    requestAdvance: 'Request Advance',
    employeeAdvances: 'Employee Advances',
    manageEmployeeSalaryAdvances: 'Manage employee salary advances',
    amount: 'Amount',
    reason: 'Reason',
    date: 'Date',
    noAdvancesFound: 'No advances found',
    currentMonthAdvancesSummary: 'Current Month Advances Summary',
    advancesOverview: 'advances overview',
    baseSalary: 'Base Salary',
    count: 'Count',
    advancesCount: 'advances',
    ofSalary: 'of salary',
    requestEmployeeAdvance: 'Request Employee Advance',
    notAllowedDuringFirstWeek: 'Process a salary advance for an employee (not allowed during first week of month)',
    selectEmployee: 'Select Employee',
    selectAnEmployee: 'Select an employee',
    advanceAmount: 'Advance Amount ($)',
    reasonForAdvance: 'Reason for Advance',
    enterReasonForAdvanceRequest: 'Enter reason for advance request...',
    monthlySalary: 'Monthly Salary',
    advancePercentage: 'Advance',
    cancel: 'Cancel',
    processAdvance: 'Process Advance',

    // Employee Forms
    addNewEmployee: 'Add New Employee',
    editEmployee: 'Edit Employee',
    fullName: 'Full Name',
    email: 'Email',
    phone: 'Phone',
    address: 'Address',
    hireDate: 'Hire Date',
    notes: 'Notes',
    changePhoto: 'Change Photo',
    remove: 'Remove',
    performanceStats: 'Performance Stats',
    lastCheckInTime: 'Last Check-in',
    employmentInfo: 'Employment Info',
    employeeId: 'Employee ID',
    attendanceAnalytics: 'Attendance Analytics',
    lateMinutes: 'Late Minutes',
    overtimeMinutes: 'Overtime Minutes',
    absentDays: 'Absent Days',
    today: 'Today',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    thisYear: 'This Year',
    previousWeek: 'Previous Week',
    previousMonth: 'Previous Month',
    previousYear: 'Previous Year',
    summary: 'Summary',
    totalLateMinutesThisMonth: 'Total Late Minutes (This Month)',
    totalOvertimeMinutesThisMonth: 'Total Overtime Minutes (This Month)',
    totalAbsentDaysThisMonth: 'Total Absent Days (This Month)',
    latePenaltyThisMonth: 'Late Penalty (This Month)',
    overtimeBonusThisMonth: 'Overtime Bonus (This Month)',
    deleteEmployee: 'Delete Employee',
    saveChanges: 'Save Changes',
    never: 'Never',
    sessions: 'sessions',

    // Service Forms
    addNewService: 'Add New Service',
    editService: 'Edit Service',
    durationMinutes: 'Duration (minutes)',
    category: 'Category',
    hair: 'Hair',
    nail: 'Nail',
    facial: 'Facial',
    massage: 'Massage',
    other: 'Other',
    updateService: 'Update Service',

    // Package Forms
    addNewPackage: 'Add New Package',
    editPackage: 'Edit Package',
    packageName: 'Package Name',
    selectServices: 'Select Services',
    totalPrice: 'Total Price ($)',
    packagePrice: 'Package Price ($)',
    autoCalculated: 'Auto-calculated',
    selectedServices: 'Selected Services',
    totalValue: 'Total Value',
    savings: 'Savings',
    updatePackage: 'Update Package',

    // Dress Forms
    addNewDress: 'Add New Dress',
    editDress: 'Edit Dress',
    addDressDetailsIncludingPhotosAndColors: 'Add dress details including photos and colors',
    dressName: 'Dress Name *',
    enterDressName: 'Enter dress name',
    rentalPriceDollar: 'Rental Price ($) *',
    photos: 'Photos',
    addPhoto: 'Add Photo',
    enterDressDescription: 'Enter dress description',
    saveDressChanges: 'Save Changes',

    // Booking Management
    newBooking: 'New Booking',
    calendar: 'Calendar',
    selectDateToViewBookings: 'Select a date to view bookings',
    bookingsFor: 'Bookings for',
    manageAppointmentsAndServices: 'Manage appointments and services',
    bookings: 'bookings',
    noBookingsForThisDate: 'No bookings for this date',
    confirmed: 'Confirmed',
    completed: 'Completed',
    cancelled: 'Cancelled',
    pending: 'Pending',
    markCompleted: 'Mark Completed',
    regularCustomer: 'Regular customer',
    prefersNaturalLook: 'prefers natural look',

    // Financial Dashboard
    servicesRevenue: 'Services Revenue',
    individualServicesOnly: 'Individual services only',
    packagesRevenue: 'Packages Revenue',
    dressRentalsRevenue: 'Dress Rentals Revenue',
    dressRentalIncome: 'Dress rental income',
    revenueByCategory: 'Revenue by Category',
    breakdownOfRevenueSources: 'Breakdown of revenue sources from completed bookings',
    topPerformingEmployees: 'Top Performing Employees',
    basedOnRevenueFromCompletedBookings: 'Based on revenue from completed bookings',
    noPerformanceDataAvailable: 'No performance data available',
    monthlyRevenueTrend: 'Monthly Revenue Trend',
    revenueOverTimeFromCompletedBookings: 'Revenue over time from completed bookings only',

    // Attendance Management
    smartAttendanceManagement: 'Smart Attendance Management',
    isNotWorkingDay: 'is not a working day',
    holiday: 'Holiday',
    employeesOnDayOff: 'employee(s) on day-off',
    exportReport: 'Export Report',
    markAttendance: 'Mark Attendance',
    presentToday: 'Present Today',
    outOfEmployees: 'Out of',
    lateArrivals: 'Late Arrivals',
    penaltyApplicable: 'Penalty applicable',
    absent: 'Absent',
    autoMarkedAfterAllowanceTime: 'Auto-marked after allowance time',
    overtime: 'Overtime',
    extraHoursWorked: 'Extra hours worked',
    minAllowance: 'min allowance',
    smartCalendar: 'Smart Calendar',
    viewAttendanceWithHolidaysAndDayOffs: 'View attendance with holidays and day-offs',
    nonWorkingDay: 'Non-working day',
    dayOff: 'Day-off',
    quickActionsAttendance: 'Quick Actions',
    bulkCheckIn: 'Bulk Check-in',
    markManualPenalty: 'Mark Manual Penalty',
    generateReport: 'Generate Report',
    smartAttendanceRecords: 'Smart Attendance Records',
    workingDay: 'Working Day',
    nonWorkingDayParentheses: 'Non-Working Day',
    checkIn: 'Check In',
    checkOut: 'Check Out',
    hours: 'Hours',
    lateOT: 'Late/OT',
    noActiveEmployeesFound: 'No active employees found',
    smartAttendanceRules: 'Smart Attendance Rules',
    latePenalty: 'Late Penalty',
    appliedProportionally: 'Applied proportionally',
    absencePenalty: 'Absence Penalty',
    excludesHolidaysDayOffs: 'Excludes holidays/day-offs',
    overtimeBonus: 'Overtime Bonus',
    after: 'After',
    smartFeatures: 'Smart Features',
    automaticallyExcludesHolidays: 'Automatically excludes holidays from absence penalties',
    respectsIndividualEmployeeDayOffs: 'Respects individual employee day-offs',
    realTimeStatusCalculation: 'Real-time status calculation based on working hours',
    proportionalPenaltiesAndBonuses: 'Proportional penalties and bonuses',
    visualCalendarWithIndicators: 'Visual calendar with holiday and day-off indicators',
    manualPenaltyApplication: 'Manual penalty application through "Mark Manual Penalty" button',
    autoAbsenceMarking: 'Auto-absence marking after allowance time passes',
    holidayDayOffWorkersNotLate: 'Holiday/day-off workers are not marked as "late"',

    // Common
    inactive: 'Inactive',
    pending: 'Pending',
    paid: 'Paid',
    cancelled: 'Cancelled',
    days: 'days',
    minutes: 'minutes',
    hours: 'hours',
    enterPlaceholder: 'Enter',
  },
  ar: {
    // Sidebar
    salonManager: 'مدير الصالون',
    managementSystem: 'نظام الإدارة',
    dashboard: 'لوحة التحكم',
    teamManagement: 'إدارة الفريق',
    serviceManagement: 'إدارة الخدمات',
    bookingManagement: 'إدارة الحجوزات',
    financialDashboard: 'لوحة المالية',
    attendance: 'الحضور',
    payroll: 'كشف المرتبات',
    customers: 'العملاء',
    settings: 'الإعدادات',
    copyright: '© 2025 سالون سيس الإصدار 1.0',

    // Dashboard
    totalRevenue: 'إجمالي الإيرادات',
    fromCompletedBookingsOnly: 'من الحجوزات المكتملة فقط',
    totalBookings: 'إجمالي الحجوزات',
    completedAndConfirmed: 'مكتملة ومؤكدة',
    activeEmployees: 'الموظفون النشطون',
    currentlyWorking: 'يعملون حالياً',
    popularService: 'الخدمة الشائعة',
    mostBookedService: 'الخدمة الأكثر حجزاً',
    workingHours: 'ساعات العمل',
    allowance: 'هامش',
    quickActions: 'الإجراءات السريعة',
    quicklyPerformCommonTasks: 'تنفيذ المهام الشائعة بسرعة',
    quickNewBooking: 'حجز جديد سريع',
    addCustomer: 'إضافة عميل',
    viewCalendar: 'عرض التقويم',
    todaysAppointments: 'مواعيد اليوم',
    appointmentsToday: 'موعد اليوم',
    noAppointmentsScheduled: 'لا توجد مواعيد مجدولة لليوم',
    createNewBooking: 'إنشاء حجز جديد',
    revenueOverview: 'نظرة عامة على الإيرادات',
    revenueTrends: 'اتجاهات الإيرادات من الحجوزات المكتملة فقط',
    weekly: 'أسبوعي',
    monthly: 'شهري',
    yearly: 'سنوي',
    daily: 'يومي',
    refresh: 'تحديث',
    viewAll: 'عرض الكل',
    appointments: 'المواعيد',

    // Team Management
    addEmployee: 'إضافة موظف',
    totalEmployees: 'إجمالي الموظفين',
    avgAttendance: 'متوسط الحضور',
    totalPayroll: 'إجمالي الرواتب',
    monthlyTotal: 'الإجمالي الشهري',
    allEmployees: 'جميع الموظفين',
    active: 'نشط',
    onLeave: 'في إجازة',
    advances: 'السلف',
    employee: 'الموظف',
    position: 'المنصب',
    status: 'الحالة',
    attendanceRate: 'معدل الحضور',
    salary: 'الراتب',
    lastCheckIn: 'آخر تسجيل دخول',
    actions: 'الإجراءات',
    viewProfile: 'عرض الملف الشخصي',
    noEmployeesFound: 'لم يتم العثور على موظفين',
    fromLastMonth: 'من الشهر الماضي',
    ofTotal: 'من الإجمالي',

    // Services Management
    services: 'الخدمات',
    packages: 'الباقات',
    dressRentals: 'تأجير الفساتين',
    addService: 'إضافة خدمة',
    addPackage: 'إضافة باقة',
    addDress: 'إضافة فستان',
    serviceName: 'اسم الخدمة',
    duration: 'المدة',
    price: 'السعر',
    description: 'الوصف',
    edit: 'تعديل',
    delete: 'حذف',
    noServicesFound: 'لم يتم العثور على خدمات',
    servicePackages: 'باقات الخدمات',
    noPackagesFound: 'لم يتم العثور على باقات',
    includedServices: 'الخدمات المشمولة',
    searchServices: 'البحث في الخدمات...',
    searchDresses: 'البحث في الفساتين...',
    allStatus: 'جميع الحالات',
    available: 'متاح',
    rented: 'مؤجر',
    maintenance: 'صيانة',
    colors: 'الألوان',
    availableColors: 'الألوان المتاحة',
    rentalPrice: 'سعر الإيجار',
    noDressesFound: 'لم يتم العثور على فساتين',

    // Employee Advances
    requestAdvance: 'طلب سلفة',
    employeeAdvances: 'سلف الموظفين',
    manageEmployeeSalaryAdvances: 'إدارة سلف رواتب الموظفين',
    amount: 'المبلغ',
    reason: 'السبب',
    date: 'التاريخ',
    noAdvancesFound: 'لم يتم العثور على سلف',
    currentMonthAdvancesSummary: 'ملخص سلف الشهر الحالي',
    advancesOverview: 'نظرة عامة على السلف',
    baseSalary: 'الراتب الأساسي',
    count: 'العدد',
    advancesCount: 'سلفة',
    ofSalary: 'من الراتب',
    requestEmployeeAdvance: 'طلب سلفة موظف',
    notAllowedDuringFirstWeek: 'معالجة سلفة راتب للموظف (غير مسموح خلال الأسبوع الأول من الشهر)',
    selectEmployee: 'اختر الموظف',
    selectAnEmployee: 'اختر موظفاً',
    advanceAmount: 'مبلغ السلفة (دولار)',
    reasonForAdvance: 'سبب السلفة',
    enterReasonForAdvanceRequest: 'أدخل سبب طلب السلفة...',
    monthlySalary: 'الراتب الشهري',
    advancePercentage: 'السلفة',
    cancel: 'إلغاء',
    processAdvance: 'معالجة السلفة',

    // Employee Forms
    addNewEmployee: 'إضافة موظف جديد',
    editEmployee: 'تعديل الموظف',
    fullName: 'الاسم الكامل',
    email: 'البريد الإلكتروني',
    phone: 'الهاتف',
    address: 'العنوان',
    hireDate: 'تاريخ التوظيف',
    notes: 'الملاحظات',
    changePhoto: 'تغيير الصورة',
    remove: 'إزالة',
    performanceStats: 'إحصائيات الأداء',
    lastCheckInTime: 'آخر تسجيل دخول',
    employmentInfo: 'معلومات التوظيف',
    employeeId: 'رقم الموظف',
    attendanceAnalytics: 'تحليلات الحضور',
    lateMinutes: 'دقائق التأخير',
    overtimeMinutes: 'دقائق العمل الإضافي',
    absentDays: 'أيام الغياب',
    today: 'اليوم',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر',
    thisYear: 'هذا العام',
    previousWeek: 'الأسبوع السابق',
    previousMonth: 'الشهر السابق',
    previousYear: 'العام السابق',
    summary: 'الملخص',
    totalLateMinutesThisMonth: 'إجمالي دقائق التأخير (هذا الشهر)',
    totalOvertimeMinutesThisMonth: 'إجمالي دقائق العمل الإضافي (هذا الشهر)',
    totalAbsentDaysThisMonth: 'إجمالي أيام الغياب (هذا الشهر)',
    latePenaltyThisMonth: 'غرامة التأخير (هذا الشهر)',
    overtimeBonusThisMonth: 'مكافأة العمل الإضافي (هذا الشهر)',
    deleteEmployee: 'حذف الموظف',
    saveChanges: 'حفظ التغييرات',
    never: 'أبداً',
    sessions: 'جلسات',

    // Service Forms
    addNewService: 'إضافة خدمة جديدة',
    editService: 'تعديل الخدمة',
    durationMinutes: 'المدة (بالدقائق)',
    category: 'الفئة',
    hair: 'الشعر',
    nail: 'الأظافر',
    facial: 'العناية بالوجه',
    massage: 'التدليك',
    other: 'أخرى',
    updateService: 'تحديث الخدمة',

    // Package Forms
    addNewPackage: 'إضافة باقة جديدة',
    editPackage: 'تعديل الباقة',
    packageName: 'اسم الباقة',
    selectServices: 'اختر الخدمات',
    totalPrice: 'السعر الإجمالي (دولار)',
    packagePrice: 'سعر الباقة (دولار)',
    autoCalculated: 'محسوب تلقائياً',
    selectedServices: 'الخدمات المختارة',
    totalValue: 'القيمة الإجمالية',
    savings: 'التوفير',
    updatePackage: 'تحديث الباقة',

    // Dress Forms
    addNewDress: 'إضافة فستان جديد',
    editDress: 'تعديل الفستان',
    addDressDetailsIncludingPhotosAndColors: 'إضافة تفاصيل الفستان بما في ذلك الصور والألوان',
    dressName: 'اسم الفستان *',
    enterDressName: 'أدخل اسم الفستان',
    rentalPriceDollar: 'سعر الإيجار (دولار) *',
    photos: 'الصور',
    addPhoto: 'إضافة صورة',
    enterDressDescription: 'أدخل وصف الفستان',
    saveDressChanges: 'حفظ التغييرات',

    // Booking Management
    newBooking: 'حجز جديد',
    calendar: 'التقويم',
    selectDateToViewBookings: 'اختر تاريخاً لعرض الحجوزات',
    bookingsFor: 'حجوزات',
    manageAppointmentsAndServices: 'إدارة المواعيد والخدمات',
    bookings: 'حجوزات',
    noBookingsForThisDate: 'لا توجد حجوزات لهذا التاريخ',
    confirmed: 'مؤكد',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    pending: 'في الانتظار',
    markCompleted: 'تحديد كمكتمل',
    regularCustomer: 'عميل منتظم',
    prefersNaturalLook: 'يفضل المظهر الطبيعي',

    // Financial Dashboard
    servicesRevenue: 'إيرادات الخدمات',
    individualServicesOnly: 'الخدمات الفردية فقط',
    packagesRevenue: 'إيرادات الباقات',
    dressRentalsRevenue: 'إيرادات تأجير الفساتين',
    dressRentalIncome: 'دخل تأجير الفساتين',
    revenueByCategory: 'الإيرادات حسب الفئة',
    breakdownOfRevenueSources: 'تفصيل مصادر الإيرادات من الحجوزات المكتملة',
    topPerformingEmployees: 'أفضل الموظفين أداءً',
    basedOnRevenueFromCompletedBookings: 'بناءً على الإيرادات من الحجوزات المكتملة',
    noPerformanceDataAvailable: 'لا توجد بيانات أداء متاحة',
    monthlyRevenueTrend: 'اتجاه الإيرادات الشهرية',
    revenueOverTimeFromCompletedBookings: 'الإيرادات عبر الزمن من الحجوزات المكتملة فقط',

    // Attendance Management
    smartAttendanceManagement: 'إدارة الحضور الذكية',
    isNotWorkingDay: 'ليس يوم عمل',
    holiday: 'عطلة',
    employeesOnDayOff: 'موظف(ين) في إجازة',
    exportReport: 'تصدير التقرير',
    markAttendance: 'تسجيل الحضور',
    presentToday: 'حاضر اليوم',
    outOfEmployees: 'من أصل',
    lateArrivals: 'المتأخرون',
    penaltyApplicable: 'غرامة قابلة للتطبيق',
    absent: 'غائب',
    autoMarkedAfterAllowanceTime: 'تم التحديد تلقائياً بعد وقت السماح',
    overtime: 'عمل إضافي',
    extraHoursWorked: 'ساعات إضافية مُعملة',
    minAllowance: 'دقيقة سماح',
    smartCalendar: 'التقويم الذكي',
    viewAttendanceWithHolidaysAndDayOffs: 'عرض الحضور مع العطل والإجازات',
    nonWorkingDay: 'يوم غير عمل',
    dayOff: 'إجازة',
    quickActionsAttendance: 'الإجراءات السريعة',
    bulkCheckIn: 'تسجيل دخول جماعي',
    markManualPenalty: 'تحديد غرامة يدوية',
    generateReport: 'إنشاء تقرير',
    smartAttendanceRecords: 'سجلات الحضور الذكية',
    workingDay: 'يوم عمل',
    nonWorkingDayParentheses: 'يوم غير عمل',
    checkIn: 'تسجيل دخول',
    checkOut: 'تسجيل خروج',
    hours: 'ساعات',
    lateOT: 'تأخير/عمل إضافي',
    noActiveEmployeesFound: 'لم يتم العثور على موظفين نشطين',
    smartAttendanceRules: 'قواعد الحضور الذكية',
    latePenalty: 'غرامة التأخير',
    appliedProportionally: 'تطبق بشكل تناسبي',
    absencePenalty: 'غرامة الغياب',
    excludesHolidaysDayOffs: 'تستثني العطل/الإجازات',
    overtimeBonus: 'مكافأة العمل الإضافي',
    after: 'بعد',
    smartFeatures: 'الميزات الذكية',
    automaticallyExcludesHolidays: 'تستثني العطل تلقائياً من غرامات الغياب',
    respectsIndividualEmployeeDayOffs: 'تحترم إجازات الموظفين الفردية',
    realTimeStatusCalculation: 'حساب الحالة في الوقت الفعلي بناءً على ساعات العمل',
    proportionalPenaltiesAndBonuses: 'غرامات ومكافآت تناسبية',
    visualCalendarWithIndicators: 'تقويم مرئي مع مؤشرات العطل والإجازات',
    manualPenaltyApplication: 'تطبيق الغرامة اليدوية من خلال زر "تحديد غرامة يدوية"',
    autoAbsenceMarking: 'تحديد الغياب التلقائي بعد انتهاء وقت السماح',
    holidayDayOffWorkersNotLate: 'العاملون في العطل/الإجازات لا يُحددون كـ"متأخرين"',

    // Common
    inactive: 'غير نشط',
    pending: 'في الانتظار',
    paid: 'مدفوع',
    cancelled: 'ملغي',
    days: 'أيام',
    minutes: 'دقائق',
    hours: 'ساعات',
    enterPlaceholder: 'أدخل',
  },
};

export function useTranslation(language: string = 'en'): Translations {
  return translations[language] || translations.en;
}