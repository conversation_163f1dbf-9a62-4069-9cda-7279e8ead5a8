import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { X, Clock, User, DollarSign, Calendar } from 'lucide-react';
import { format, addDays } from 'date-fns';
import { useEmployees, useServices, useServicePackages, useDressRentals, useBookings, useCustomers } from '../../hooks/useDatabase';
import { Booking, DressRentalPeriod } from '../../types';

interface BookingFormProps {
  booking?: Booking | null;
  selectedDate: Date;
  onClose: () => void;
}

export default function BookingForm({ booking, selectedDate, onClose }: BookingFormProps) {
  const { employees } = useEmployees();
  const { services } = useServices();
  const { packages } = useServicePackages();
  const { dresses, isDressAvailable } = useDressRentals();
  const { customers, addCustomer } = useCustomers();
  const { addBooking, updateBooking } = useBookings();

  const [formData, setFormData] = useState({
    title: '',
    date: format(selectedDate, 'yyyy-MM-dd'),
    notes: '',
    customerName: '',
    startTime: '',
    endTime: '',
    selectedEmployees: [] as string[],
    selectedServices: [] as string[],
    selectedPackages: [] as string[],
    selectedDresses: [] as string[],
    dressRentalPeriods: [] as DressRentalPeriod[],
    discountPercentage: 0,
    depositAmount: 0,
    manualPriceOverride: false,
    manualTotalAmount: 0,
  });

  // Customer search state
  const [customerSearchTerm, setCustomerSearchTerm] = useState('');
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  const [filteredCustomers, setFilteredCustomers] = useState(customers);

  // Dress availability state - optimized
  const [dressAvailability, setDressAvailability] = useState<Record<string, boolean>>({});
  const [availabilityChecked, setAvailabilityChecked] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (booking) {
      setFormData({
        title: booking.title || '',
        date: booking.date,
        notes: booking.notes || '',
        customerName: booking.customerName,
        startTime: booking.startTime,
        endTime: booking.endTime,
        selectedEmployees: booking.employeeIds,
        selectedServices: booking.serviceIds,
        selectedPackages: booking.packageIds,
        selectedDresses: booking.dressIds,
        dressRentalPeriods: booking.dressRentalPeriods || [],
        discountPercentage: booking.subtotal > 0 ? (booking.discount / booking.subtotal) * 100 : 0,
        depositAmount: booking.deposit,
        manualPriceOverride: false,
        manualTotalAmount: booking.totalAmount,
      });
      setCustomerSearchTerm(booking.customerName);
    }
  }, [booking]);

  // Filter customers based on search term
  useEffect(() => {
    if (customerSearchTerm.trim() === '') {
      setFilteredCustomers([]);
      setShowCustomerDropdown(false);
    } else {
      const filtered = customers.filter(customer =>
        customer.name.toLowerCase().includes(customerSearchTerm.toLowerCase()) ||
        customer.phone.includes(customerSearchTerm) ||
        customer.email.toLowerCase().includes(customerSearchTerm.toLowerCase())
      );
      setFilteredCustomers(filtered);
      setShowCustomerDropdown(true);
    }
  }, [customerSearchTerm, customers]);

  // Optimized dress availability checking with debouncing
  const checkDressAvailability = useCallback(async (dressId: string, startDate: string, endDate: string) => {
    const periodKey = `${dressId}-${startDate}-${endDate}`;
    
    // Skip if already checked this exact period
    if (availabilityChecked.has(periodKey)) {
      return;
    }

    try {
      const isAvailable = await isDressAvailable(
        dressId, 
        startDate, 
        endDate,
        booking?.id // Exclude current booking when editing
      );
      
      setDressAvailability(prev => ({
        ...prev,
        [dressId]: isAvailable
      }));
      
      setAvailabilityChecked(prev => new Set(prev).add(periodKey));
    } catch (error) {
      console.error('Error checking dress availability:', error);
      setDressAvailability(prev => ({
        ...prev,
        [dressId]: false
      }));
    }
  }, [isDressAvailable, booking?.id, availabilityChecked]);

  // Debounced availability checking
  useEffect(() => {
    if (formData.dressRentalPeriods.length === 0) {
      setDressAvailability({});
      setAvailabilityChecked(new Set());
      return;
    }

    const timeoutId = setTimeout(() => {
      formData.dressRentalPeriods.forEach(period => {
        if (period.startDate && period.endDate) {
          checkDressAvailability(period.dressId, period.startDate, period.endDate);
        }
      });
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [formData.dressRentalPeriods, checkDressAvailability]);

  const handleCustomerSearch = (value: string) => {
    setCustomerSearchTerm(value);
    setFormData(prev => ({ ...prev, customerName: value }));
  };

  const handleCustomerSelect = (customer: any) => {
    setCustomerSearchTerm(customer.name);
    setFormData(prev => ({ ...prev, customerName: customer.name }));
    setShowCustomerDropdown(false);
  };

  const handleAddNewCustomer = async () => {
    if (!customerSearchTerm.trim()) return;

    try {
      // Create new customer with the entered name
      await addCustomer({
        name: customerSearchTerm.trim(),
        phone: '',
        email: '',
        address: '',
        notes: `Added from booking on ${format(new Date(), 'yyyy-MM-dd')}`,
        instagram: '',
        facebook: '',
        whatsapp: '',
        totalBookings: 0,
        totalSpent: 0,
        customerSince: new Date().toISOString(),
        favoriteServices: [],
      });

      // Update form data and close dropdown
      setFormData(prev => ({ ...prev, customerName: customerSearchTerm.trim() }));
      setShowCustomerDropdown(false);
      
      // Show success message
      alert(`Customer "${customerSearchTerm.trim()}" has been added to your customer list!`);
    } catch (error) {
      console.error('Error adding new customer:', error);
      alert('Failed to add new customer. Please try again.');
    }
  };

  const handleEmployeeToggle = (employeeId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedEmployees: prev.selectedEmployees.includes(employeeId)
        ? prev.selectedEmployees.filter(id => id !== employeeId)
        : [...prev.selectedEmployees, employeeId]
    }));
  };

  const handleServiceToggle = (serviceId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedServices: prev.selectedServices.includes(serviceId)
        ? prev.selectedServices.filter(id => id !== serviceId)
        : [...prev.selectedServices, serviceId]
    }));
  };

  const handlePackageToggle = (packageId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedPackages: prev.selectedPackages.includes(packageId)
        ? prev.selectedPackages.filter(id => id !== packageId)
        : [...prev.selectedPackages, packageId]
    }));
  };

  const handleDressToggle = (dressId: string) => {
    setFormData(prev => {
      const isSelected = prev.selectedDresses.includes(dressId);
      
      if (isSelected) {
        // Remove dress and its rental period
        const newDressRentalPeriods = prev.dressRentalPeriods.filter(period => period.dressId !== dressId);
        
        // Clear availability for this dress
        setDressAvailability(prevAvail => {
          const newAvail = { ...prevAvail };
          delete newAvail[dressId];
          return newAvail;
        });
        
        // Clear checked status for this dress
        setAvailabilityChecked(prevChecked => {
          const newChecked = new Set(prevChecked);
          Array.from(newChecked).forEach(key => {
            if (key.startsWith(`${dressId}-`)) {
              newChecked.delete(key);
            }
          });
          return newChecked;
        });
        
        return {
          ...prev,
          selectedDresses: prev.selectedDresses.filter(id => id !== dressId),
          dressRentalPeriods: newDressRentalPeriods
        };
      } else {
        // Add dress with default rental period (same day)
        const defaultPeriod: DressRentalPeriod = {
          dressId,
          startDate: prev.date,
          endDate: prev.date
        };
        
        return {
          ...prev,
          selectedDresses: [...prev.selectedDresses, dressId],
          dressRentalPeriods: [...prev.dressRentalPeriods, defaultPeriod]
        };
      }
    });
  };

  const handleDressRentalPeriodChange = (dressId: string, field: 'startDate' | 'endDate', value: string) => {
    setFormData(prev => ({
      ...prev,
      dressRentalPeriods: prev.dressRentalPeriods.map(period =>
        period.dressId === dressId
          ? { ...period, [field]: value }
          : period
      )
    }));

    // Clear availability check for this dress to trigger re-check
    setAvailabilityChecked(prevChecked => {
      const newChecked = new Set(prevChecked);
      Array.from(newChecked).forEach(key => {
        if (key.startsWith(`${dressId}-`)) {
          newChecked.delete(key);
        }
      });
      return newChecked;
    });
  };

  const calculateSubtotal = () => {
    let total = 0;

    // Add service prices
    formData.selectedServices.forEach(serviceId => {
      const service = services.find(s => s.id === serviceId);
      if (service) total += service.price;
    });

    // Add package prices
    formData.selectedPackages.forEach(packageId => {
      const pkg = packages.find(p => p.id === packageId);
      if (pkg) total += pkg.discountedPrice;
    });

    // Add dress rental prices (calculate based on rental period)
    formData.dressRentalPeriods.forEach(period => {
      const dress = dresses.find(d => d.id === period.dressId);
      if (dress) {
        // Calculate number of days
        const startDate = new Date(period.startDate);
        const endDate = new Date(period.endDate);
        const daysDiff = Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1);
        total += dress.rentalPrice * daysDiff;
      }
    });

    return total;
  };

  const subtotal = calculateSubtotal();
  const discount = (subtotal * formData.discountPercentage) / 100;
  const totalAmount = formData.manualPriceOverride ? formData.manualTotalAmount : subtotal - discount;
  const amountDue = totalAmount - formData.depositAmount;

  const handleQuickDiscount = (percentage: number) => {
    setFormData(prev => ({ ...prev, discountPercentage: percentage }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check dress availability before submitting
    const hasUnavailableDresses = Object.values(dressAvailability).some(available => !available);
    if (hasUnavailableDresses) {
      alert('Some selected dresses are not available for the chosen dates. Please adjust the rental periods or select different dresses.');
      return;
    }

    const bookingData = {
      customerId: undefined,
      customerName: formData.customerName,
      title: formData.title,
      date: formData.date,
      startTime: formData.startTime,
      endTime: formData.endTime,
      employeeIds: formData.selectedEmployees,
      serviceIds: formData.selectedServices,
      packageIds: formData.selectedPackages,
      dressIds: formData.selectedDresses,
      dressRentalPeriods: formData.dressRentalPeriods,
      subtotal,
      discount,
      deposit: formData.depositAmount,
      totalAmount,
      amountDue,
      status: 'confirmed' as const,
      notes: formData.notes,
    };

    if (booking) {
      await updateBooking(booking.id, bookingData);
    } else {
      await addBooking(bookingData);
    }

    onClose();
  };

  const activeEmployees = employees.filter(emp => emp.status === 'active');
  const activeServices = services.filter(service => service.isActive);
  const activePackages = packages.filter(pkg => pkg.isActive);
  const availableDresses = dresses.filter(dress => dress.status === 'available' || dress.status === 'rented');

  const getDressRentalPeriod = (dressId: string) => {
    return formData.dressRentalPeriods.find(period => period.dressId === dressId);
  };

  const calculateDressRentalDays = (dressId: string) => {
    const period = getDressRentalPeriod(dressId);
    if (!period) return 1;
    
    const startDate = new Date(period.startDate);
    const endDate = new Date(period.endDate);
    return Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1);
  };

  const calculateDressRentalCost = (dressId: string) => {
    const dress = dresses.find(d => d.id === dressId);
    if (!dress) return 0;
    
    const days = calculateDressRentalDays(dressId);
    return dress.rentalPrice * days;
  };

  // Memoized dress availability check
  const isDressCurrentlyAvailable = useMemo(() => {
    return (dressId: string) => {
      if (formData.selectedDresses.includes(dressId)) {
        return dressAvailability[dressId] !== false;
      }
      return true;
    };
  }, [formData.selectedDresses, dressAvailability]);

  // Check if any dress is being checked for availability
  const isCheckingAnyDress = useMemo(() => {
    return formData.selectedDresses.some(dressId => {
      const period = getDressRentalPeriod(dressId);
      if (!period || !period.startDate || !period.endDate) return false;
      
      const periodKey = `${dressId}-${period.startDate}-${period.endDate}`;
      return !availabilityChecked.has(periodKey) && !(dressId in dressAvailability);
    });
  }, [formData.selectedDresses, formData.dressRentalPeriods, availabilityChecked, dressAvailability]);

  // Check if customer name matches any existing customer exactly
  const exactCustomerMatch = customers.find(customer => 
    customer.name.toLowerCase() === customerSearchTerm.toLowerCase()
  );

  // Show "Add as new customer" option when:
  // 1. There's text in the search field
  // 2. No exact match exists
  // 3. Dropdown is visible
  const showAddNewCustomerOption = customerSearchTerm.trim() && !exactCustomerMatch && showCustomerDropdown;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {booking ? 'Edit Booking' : 'Create New Booking'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Fill in the booking details for the selected date
          </p>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Title (Optional)
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter booking title"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Date *
              </label>
              <input
                type="date"
                value={formData.date}
                onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                required
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Notes (Optional)
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              placeholder="Enter any additional notes"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          {/* Customer Search */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Customer (Optional)
            </label>
            <input
              type="text"
              value={customerSearchTerm}
              onChange={(e) => handleCustomerSearch(e.target.value)}
              placeholder="Search for existing customer or enter new name"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              onFocus={() => customerSearchTerm && setShowCustomerDropdown(true)}
              onBlur={() => setTimeout(() => setShowCustomerDropdown(false), 200)}
            />
            
            {/* Customer Dropdown */}
            {showCustomerDropdown && (
              <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                {/* Existing customers */}
                {filteredCustomers.map((customer) => (
                  <div
                    key={customer.id}
                    onClick={() => handleCustomerSelect(customer)}
                    className="p-3 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                  >
                    <div className="font-medium text-gray-900 dark:text-white">{customer.name}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">{customer.phone}</div>
                  </div>
                ))}
                
                {/* Add new customer option - Always show when typing and no exact match */}
                {showAddNewCustomerOption && (
                  <div
                    onClick={handleAddNewCustomer}
                    className="p-3 hover:bg-blue-50 dark:hover:bg-blue-900/20 cursor-pointer border-t border-gray-200 dark:border-gray-600 text-blue-600 dark:text-blue-400"
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">+</span>
                      <span>Add "{customerSearchTerm}" as new customer</span>
                    </div>
                    <div className="text-xs text-blue-500 dark:text-blue-400 mt-1">
                      This will save the customer to your customer list
                    </div>
                  </div>
                )}

                {/* No results message */}
                {filteredCustomers.length === 0 && !showAddNewCustomerOption && customerSearchTerm.trim() && (
                  <div className="p-3 text-gray-500 dark:text-gray-400 text-center">
                    No customers found
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Time Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Start Time (Optional)
              </label>
              <div className="relative">
                <input
                  type="time"
                  value={formData.startTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                End Time (Optional)
              </label>
              <div className="relative">
                <input
                  type="time"
                  value={formData.endTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </div>

          {/* Employee Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Employees (Optional)
            </label>
            <div className="grid grid-cols-2 gap-3">
              {activeEmployees.map(employee => (
                <label key={employee.id} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.selectedEmployees.includes(employee.id)}
                    onChange={() => handleEmployeeToggle(employee.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">{employee.name}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Services Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Services (Optional)
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {activeServices.map(service => (
                <label key={service.id} className="flex items-center justify-between p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={formData.selectedServices.includes(service.id)}
                      onChange={() => handleServiceToggle(service.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">{service.name}</span>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">${service.price}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Packages Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Packages (Optional)
            </label>
            <div className="space-y-3">
              {activePackages.map(pkg => (
                <label key={pkg.id} className="flex items-center justify-between p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={formData.selectedPackages.includes(pkg.id)}
                      onChange={() => handlePackageToggle(pkg.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">{pkg.name}</span>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">${pkg.discountedPrice}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Available Dresses with Rental Periods */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Available Dresses with Rental Periods (Optional)
            </label>
            {isCheckingAnyDress && (
              <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className="text-sm text-blue-800 dark:text-blue-300">
                  <span className="inline-block animate-spin mr-2">⏳</span>
                  Checking availability for selected dates...
                </p>
              </div>
            )}
            <div className="space-y-4">
              {availableDresses.map(dress => {
                const isSelected = formData.selectedDresses.includes(dress.id);
                const rentalPeriod = getDressRentalPeriod(dress.id);
                const rentalDays = calculateDressRentalDays(dress.id);
                const rentalCost = calculateDressRentalCost(dress.id);
                const isAvailable = isDressCurrentlyAvailable(dress.id);

                return (
                  <div key={dress.id} className={`border rounded-lg p-4 ${
                    isSelected && !isAvailable 
                      ? 'border-red-300 bg-red-50 dark:border-red-600 dark:bg-red-900/20' 
                      : 'border-gray-300 dark:border-gray-600'
                  }`}>
                    <label className="flex items-center justify-between cursor-pointer mb-3">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => handleDressToggle(dress.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <div>
                          <span className="text-sm font-medium text-gray-900 dark:text-white">{dress.name}</span>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            ${dress.rentalPrice}/day • {dress.availableColors.join(', ')}
                          </div>
                          {isSelected && !isAvailable && (
                            <div className="text-xs text-red-600 dark:text-red-400 mt-1">
                              ⚠️ Not available for selected dates
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          ${rentalCost.toFixed(2)}
                        </div>
                        {isSelected && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {rentalDays} day{rentalDays !== 1 ? 's' : ''}
                          </div>
                        )}
                      </div>
                    </label>

                    {/* Rental Period Selection */}
                    {isSelected && rentalPeriod && (
                      <div className={`mt-3 p-3 rounded-lg ${
                        isAvailable 
                          ? 'bg-blue-50 dark:bg-blue-900/20' 
                          : 'bg-red-50 dark:bg-red-900/20'
                      }`}>
                        <h4 className={`text-sm font-medium mb-3 flex items-center space-x-2 ${
                          isAvailable 
                            ? 'text-blue-800 dark:text-blue-300' 
                            : 'text-red-800 dark:text-red-300'
                        }`}>
                          <Calendar className="h-4 w-4" />
                          <span>Rental Period</span>
                          {!isAvailable && <span className="text-xs">(Conflict detected)</span>}
                        </h4>
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className={`block text-xs font-medium mb-1 ${
                              isAvailable 
                                ? 'text-blue-700 dark:text-blue-300' 
                                : 'text-red-700 dark:text-red-300'
                            }`}>
                              Start Date
                            </label>
                            <input
                              type="date"
                              value={rentalPeriod.startDate}
                              onChange={(e) => handleDressRentalPeriodChange(dress.id, 'startDate', e.target.value)}
                              min={formData.date}
                              className={`w-full px-2 py-1 text-sm border rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                                isAvailable 
                                  ? 'border-blue-300 dark:border-blue-600' 
                                  : 'border-red-300 dark:border-red-600'
                              }`}
                            />
                          </div>
                          <div>
                            <label className={`block text-xs font-medium mb-1 ${
                              isAvailable 
                                ? 'text-blue-700 dark:text-blue-300' 
                                : 'text-red-700 dark:text-red-300'
                            }`}>
                              End Date
                            </label>
                            <input
                              type="date"
                              value={rentalPeriod.endDate}
                              onChange={(e) => handleDressRentalPeriodChange(dress.id, 'endDate', e.target.value)}
                              min={rentalPeriod.startDate}
                              className={`w-full px-2 py-1 text-sm border rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                                isAvailable 
                                  ? 'border-blue-300 dark:border-blue-600' 
                                  : 'border-red-300 dark:border-red-600'
                              }`}
                            />
                          </div>
                        </div>
                        <div className={`mt-2 text-xs ${
                          isAvailable 
                            ? 'text-blue-600 dark:text-blue-400' 
                            : 'text-red-600 dark:text-red-400'
                        }`}>
                          Total: {rentalDays} day{rentalDays !== 1 ? 's' : ''} × ${dress.rentalPrice} = ${rentalCost.toFixed(2)}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Pricing Section */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Pricing</h3>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.manualPriceOverride}
                  onChange={(e) => setFormData(prev => ({ ...prev, manualPriceOverride: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Manual Price Override</span>
              </label>
            </div>

            {formData.manualPriceOverride && (
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Manual Total Amount
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.manualTotalAmount}
                  onChange={(e) => setFormData(prev => ({ ...prev, manualTotalAmount: parseFloat(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Discount Percentage
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={formData.discountPercentage}
                  onChange={(e) => setFormData(prev => ({ ...prev, discountPercentage: parseFloat(e.target.value) || 0 }))}
                  disabled={formData.manualPriceOverride}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50"
                />
                
                {/* Quick Discount Options */}
                {!formData.manualPriceOverride && (
                  <div className="mt-2">
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">Quick options:</p>
                    <div className="flex space-x-2">
                      {[5, 10, 15, 20].map(percentage => (
                        <button
                          key={percentage}
                          type="button"
                          onClick={() => handleQuickDiscount(percentage)}
                          className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                        >
                          {percentage}%
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Deposit Amount
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.depositAmount}
                  onChange={(e) => setFormData(prev => ({ ...prev, depositAmount: parseFloat(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>

            {/* Pricing Summary */}
            <div className="mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="space-y-2">
                {!formData.manualPriceOverride && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Subtotal:</span>
                    <span className="font-medium text-gray-900 dark:text-white">${subtotal.toFixed(2)}</span>
                  </div>
                )}
                {formData.discountPercentage > 0 && !formData.manualPriceOverride && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Discount ({formData.discountPercentage}%):</span>
                    <span className="font-medium text-red-600 dark:text-red-400">-${discount.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Total Amount:</span>
                  <span className="font-medium text-gray-900 dark:text-white">${totalAmount.toFixed(2)}</span>
                </div>
                {formData.depositAmount > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Deposit:</span>
                    <span className="font-medium text-green-600 dark:text-green-400">${formData.depositAmount.toFixed(2)}</span>
                  </div>
                )}
                <div className="border-t border-gray-200 dark:border-gray-600 pt-2">
                  <div className="flex justify-between text-lg font-bold">
                    <span className="text-gray-900 dark:text-white">Amount Due:</span>
                    <span className="text-gray-900 dark:text-white">${amountDue.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isCheckingAnyDress || Object.values(dressAvailability).some(available => !available)}
              className="px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {booking ? 'Update Booking' : 'Create Booking'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}