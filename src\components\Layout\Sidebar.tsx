import React from 'react';
import { 
  LayoutDashboard, 
  Users, 
  Clock, 
  DollarSign,
  Scissors,
  Calendar,
  UserCheck,
  BarChart3,
  Settings, 
  Menu,
  X
} from 'lucide-react';
import { Page } from '../../types';
import { useSettings } from '../../hooks/useDatabase';
import { useTranslation } from '../../utils/translations';

interface SidebarProps {
  currentPage: Page;
  onPageChange: (page: Page) => void;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  isMobile: boolean;
  rtlMode: boolean;
}

export default function Sidebar({ 
  currentPage, 
  onPageChange, 
  isCollapsed, 
  onToggleCollapse,
  isMobile,
  rtlMode
}: SidebarProps) {
  const { settings } = useSettings(true);
  const t = useTranslation(settings?.language || 'en');

  const menuItems = [
    { id: 'dashboard' as Page, label: t.dashboard, icon: LayoutDashboard },
    { id: 'employees' as Page, label: t.teamManagement, icon: Users },
    { id: 'services' as Page, label: t.serviceManagement, icon: Scissors },
    { id: 'bookings' as Page, label: t.bookingManagement, icon: Calendar },
    { id: 'financial' as Page, label: t.financialDashboard, icon: BarChart3 },
    { id: 'attendance' as Page, label: t.attendance, icon: Clock },
    { id: 'payroll' as Page, label: t.payroll, icon: DollarSign },
    { id: 'customers' as Page, label: t.customers, icon: UserCheck },
    { id: 'settings' as Page, label: t.settings, icon: Settings },
  ];

  return (
    <>
      {/* Mobile overlay */}
      {isMobile && !isCollapsed && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggleCollapse}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed top-0 h-full bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 z-50 transition-all duration-300 ease-in-out
        ${rtlMode 
          ? `right-0 border-l ${isCollapsed ? (isMobile ? 'translate-x-full' : 'w-16') : 'translate-x-0 w-64'}`
          : `left-0 border-r ${isCollapsed ? (isMobile ? '-translate-x-full' : 'w-16') : 'translate-x-0 w-64'}`
        }
        ${isMobile ? 'lg:relative lg:translate-x-0' : ''}
      `}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className={`flex items-center ${
            rtlMode 
              ? `${isCollapsed && !isMobile ? 'lg:flex-row-reverse lg:justify-center' : 'flex-row-reverse'} space-x-reverse space-x-3`
              : `${isCollapsed && !isMobile ? 'lg:justify-center' : ''} space-x-3`
          }`}>
            <div className="p-2 bg-primary-600 rounded-lg">
              <Scissors className="h-6 w-6 text-white" />
            </div>
            <div className={`${isCollapsed && !isMobile ? 'lg:hidden' : ''}`}>
              <h1 className={`text-xl font-bold text-gray-900 dark:text-white ${rtlMode ? 'text-right' : 'text-left'}`}>
                {t.salonManager}
              </h1>
              <p className={`text-xs text-gray-500 dark:text-gray-400 ${rtlMode ? 'text-right' : 'text-left'}`}>
                {t.managementSystem}
              </p>
            </div>
          </div>
          
          <button
            onClick={onToggleCollapse}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            {(isCollapsed && !isMobile) || (!isCollapsed && isMobile) ? 
              <Menu className="h-5 w-5" /> : 
              <X className="h-5 w-5" />
            }
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.id;
              
              return (
                <li key={item.id}>
                  <button
                    onClick={() => {
                      onPageChange(item.id);
                      if (isMobile) onToggleCollapse();
                    }}
                    className={`
                      w-full flex items-center px-3 py-3 rounded-lg transition-all duration-200
                      ${isActive 
                        ? 'bg-primary-600 text-white shadow-lg' 
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                      }
                      ${isCollapsed && !isMobile ? 'lg:justify-center' : ''}
                    `}
                  >
                    {/* Icon and Text Container */}
                    <div className={`flex items-center w-full ${
                      rtlMode 
                        ? 'flex-row space-x-3 justify-between'
                        : 'space-x-3'
                    }`}>
                      <Icon className={`h-5 w-5 ${rtlMode ? 'ml-3' : ''} ${isActive ? 'text-white' : ''} flex-shrink-0`} />
                      <span className={`font-medium flex-1 ${isCollapsed && !isMobile ? 'lg:hidden' : ''} ${rtlMode ? 'text-right' : 'text-left'}`}>
                        {item.label}
                      </span>
                    </div>
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Footer */}
        <div className={`p-4 border-t border-gray-200 dark:border-gray-700 ${isCollapsed && !isMobile ? 'lg:text-center' : rtlMode ? 'text-right' : 'text-left'}`}>
          <p className={`text-xs text-gray-500 dark:text-gray-400 ${isCollapsed && !isMobile ? 'lg:hidden' : ''}`}>
            {t.copyright}
          </p>
        </div>
      </div>
    </>
  );
}