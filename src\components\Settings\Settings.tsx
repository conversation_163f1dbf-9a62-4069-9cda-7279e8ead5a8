import React, { useState, useEffect } from 'react';
import { Save, Clock, Calendar, DollarSign, Palette, Users, Globe, Plus, Trash2, Edit } from 'lucide-react';
import { useSettings, useHolidays, useEmployeeDayOffs, useEmployees } from '../../hooks/useDatabase';
import { format } from 'date-fns';

const daysOfWeek = [
  { id: 'monday', label: 'Monday', short: 'Mon' },
  { id: 'tuesday', label: 'Tuesday', short: 'Tue' },
  { id: 'wednesday', label: 'Wednesday', short: 'Wed' },
  { id: 'thursday', label: 'Thursday', short: 'Thu' },
  { id: 'friday', label: 'Friday', short: 'Fri' },
  { id: 'saturday', label: 'Saturday', short: 'Sat' },
  { id: 'sunday', label: 'Sunday', short: 'Sun' },
];

const primaryColors = [
  { id: 'blue', name: 'Blue', color: '#2563eb' },
  { id: 'indigo', name: 'Indigo', color: '#4f46e5' },
  { id: 'purple', name: 'Purple', color: '#7c3aed' },
  { id: 'pink', name: 'Pink', color: '#db2777' },
  { id: 'red', name: 'Red', color: '#dc2626' },
  { id: 'orange', name: 'Orange', color: '#ea580c' },
  { id: 'amber', name: 'Amber', color: '#d97706' },
  { id: 'yellow', name: 'Yellow', color: '#ca8a04' },
  { id: 'lime', name: 'Lime', color: '#65a30d' },
  { id: 'green', name: 'Green', color: '#16a34a' },
  { id: 'emerald', name: 'Emerald', color: '#059669' },
  { id: 'teal', name: 'Teal', color: '#0d9488' },
  { id: 'cyan', name: 'Cyan', color: '#0891b2' },
  { id: 'sky', name: 'Sky', color: '#0284c7' },
];

export default function Settings() {
  const { settings, updateSettings, loading } = useSettings(true);
  const { holidays, addHoliday, deleteHoliday } = useHolidays();
  const { dayOffs, addDayOff, deleteDayOff } = useEmployeeDayOffs();
  const { employees } = useEmployees();
  const [activeTab, setActiveTab] = useState('attendance');
  const [formData, setFormData] = useState(() => ({
    workingDays: [] as string[],
    workingHours: { start: '09:00', end: '17:00' },
    attendanceRules: {
      latePenalty: 50,
      absencePenalty: 100,
      allowanceTime: 15,
      overtimeReward: 25,
      lateDaysForPenalty: 3,
      absentDaysForPenalty: 2,
    },
    darkMode: false,
    rtlMode: false,
    language: 'en',
    primaryColor: 'blue',
    currency: 'USD',
    timezone: 'UTC',
  }));

  // Holiday form state
  const [showHolidayForm, setShowHolidayForm] = useState(false);
  const [holidayForm, setHolidayForm] = useState({
    name: '',
    date: '',
    type: 'national' as 'national' | 'religious' | 'company' | 'other',
    description: '',
    isRecurring: false,
  });

  // Day-off form state
  const [showDayOffForm, setShowDayOffForm] = useState(false);
  const [dayOffForm, setDayOffForm] = useState({
    employeeId: '',
    date: '',
    reason: '',
    type: 'vacation' as 'sick' | 'vacation' | 'personal' | 'emergency' | 'other',
    notes: '',
  });

  // Update form data when settings load
  useEffect(() => {
    if (settings) {
      setFormData({
        workingDays: settings.workingDays || ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        workingHours: settings.workingHours || { start: '09:00', end: '17:00' },
        attendanceRules: {
          latePenalty: settings.attendanceRules?.latePenalty || 50,
          absencePenalty: settings.attendanceRules?.absencePenalty || 100,
          allowanceTime: settings.attendanceRules?.allowanceTime || 15,
          overtimeReward: settings.attendanceRules?.overtimeReward || 25,
          lateDaysForPenalty: settings.attendanceRules?.lateDaysForPenalty || 3,
          absentDaysForPenalty: settings.attendanceRules?.absentDaysForPenalty || 2,
        },
        darkMode: settings.darkMode || false,
        rtlMode: settings.rtlMode || false,
        language: settings.language || 'en',
        primaryColor: settings.primaryColor || 'blue',
        currency: settings.currency || 'USD',
        timezone: settings.timezone || 'UTC',
      });
    }
  }, [settings]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (updateSettings) {
      await updateSettings(formData);
      alert('Settings saved successfully!');
    }
  };

  const handleWorkingDayChange = (dayId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      workingDays: checked
        ? [...prev.workingDays, dayId]
        : prev.workingDays.filter(day => day !== dayId)
    }));
  };

  const handleAddHoliday = async (e: React.FormEvent) => {
    e.preventDefault();
    await addHoliday(holidayForm);
    setHolidayForm({
      name: '',
      date: '',
      type: 'national',
      description: '',
      isRecurring: false,
    });
    setShowHolidayForm(false);
  };

  const handleAddDayOff = async (e: React.FormEvent) => {
    e.preventDefault();
    await addDayOff({
      ...dayOffForm,
      status: 'approved', // Auto-approve for now
    });
    setDayOffForm({
      employeeId: '',
      date: '',
      reason: '',
      type: 'vacation',
      notes: '',
    });
    setShowDayOffForm(false);
  };

  const tabs = [
    { id: 'working', label: 'Working Days & Hours', icon: Clock },
    { id: 'holidays', label: 'Holidays & Day Offs', icon: Calendar },
    { id: 'attendance', label: 'Attendance Rules', icon: Users },
    { id: 'layout', label: 'Layout', icon: Palette },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-gray-500 dark:text-gray-400">Unable to load settings</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
        </div>
        <button
          onClick={handleSubmit}
          className="flex items-center space-x-2 bg-gray-900 dark:bg-gray-700 text-white px-4 py-2 rounded-lg hover:bg-gray-800 dark:hover:bg-gray-600 transition-colors"
        >
          <Save className="h-4 w-4" />
          <span>Save All Settings</span>
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors
                    ${activeTab === tab.id
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                    }
                  `}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'working' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Working Days & Hours</h3>
                
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      Working Days
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {daysOfWeek.map((day) => (
                        <label key={day.id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={formData.workingDays.includes(day.id)}
                            onChange={(e) => handleWorkingDayChange(day.id, e.target.checked)}
                            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          />
                          <span className="text-sm text-gray-700 dark:text-gray-300">{day.label}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Start Time
                      </label>
                      <input
                        type="time"
                        value={formData.workingHours.start}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          workingHours: { ...prev.workingHours, start: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        End Time
                      </label>
                      <input
                        type="time"
                        value={formData.workingHours.end}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          workingHours: { ...prev.workingHours, end: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'holidays' && (
            <div className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Holidays & Day Offs</h3>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">
                      Manage company holidays and individual employee day-offs
                    </p>
                  </div>
                  <div className="flex space-x-3">
                    <button
                      onClick={() => setShowDayOffForm(true)}
                      className="flex items-center space-x-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
                    >
                      <Plus className="h-4 w-4" />
                      <span>Add Day Off</span>
                    </button>
                    <button
                      onClick={() => setShowHolidayForm(true)}
                      className="flex items-center space-x-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors"
                    >
                      <Plus className="h-4 w-4" />
                      <span>Add Holiday</span>
                    </button>
                  </div>
                </div>

                {/* Holidays Section */}
                <div className="mb-8">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-4">Company Holidays</h4>
                  <div className="space-y-3">
                    {holidays.map((holiday) => (
                      <div key={holiday.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <Calendar className="h-4 w-4 text-primary-600" />
                            <div>
                              <h5 className="font-medium text-gray-900 dark:text-white">{holiday.name}</h5>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {format(new Date(holiday.date), 'MMMM d, yyyy')} • {holiday.type}
                                {holiday.isRecurring && ' • Recurring'}
                              </p>
                              {holiday.description && (
                                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{holiday.description}</p>
                              )}
                            </div>
                          </div>
                        </div>
                        <button
                          onClick={() => deleteHoliday(holiday.id)}
                          className="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                    {holidays.length === 0 && (
                      <p className="text-gray-500 dark:text-gray-400 text-center py-8">No holidays configured</p>
                    )}
                  </div>
                </div>

                {/* Employee Day-offs Section */}
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-4">Employee Day-offs</h4>
                  <div className="space-y-3">
                    {dayOffs.map((dayOff) => {
                      const employee = employees.find(emp => emp.id === dayOff.employeeId);
                      return (
                        <div key={dayOff.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3">
                              <Users className="h-4 w-4 text-green-600" />
                              <div>
                                <h5 className="font-medium text-gray-900 dark:text-white">
                                  {employee?.name || 'Unknown Employee'}
                                </h5>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                  {format(new Date(dayOff.date), 'MMMM d, yyyy')} • {dayOff.type} • {dayOff.status}
                                </p>
                                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{dayOff.reason}</p>
                                {dayOff.notes && (
                                  <p className="text-sm text-gray-500 dark:text-gray-400">{dayOff.notes}</p>
                                )}
                              </div>
                            </div>
                          </div>
                          <button
                            onClick={() => deleteDayOff(dayOff.id)}
                            className="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      );
                    })}
                    {dayOffs.length === 0 && (
                      <p className="text-gray-500 dark:text-gray-400 text-center py-8">No day-offs configured</p>
                    )}
                  </div>
                </div>

                {/* Holiday Form Modal */}
                {showHolidayForm && (
                  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-md">
                      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Add Holiday</h3>
                        <button
                          onClick={() => setShowHolidayForm(false)}
                          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                      <form onSubmit={handleAddHoliday} className="p-6 space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Holiday Name
                          </label>
                          <input
                            type="text"
                            value={holidayForm.name}
                            onChange={(e) => setHolidayForm(prev => ({ ...prev, name: e.target.value }))}
                            required
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              Date
                            </label>
                            <input
                              type="date"
                              value={holidayForm.date}
                              onChange={(e) => setHolidayForm(prev => ({ ...prev, date: e.target.value }))}
                              required
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              Type
                            </label>
                            <select
                              value={holidayForm.type}
                              onChange={(e) => setHolidayForm(prev => ({ ...prev, type: e.target.value as any }))}
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            >
                              <option value="national">National</option>
                              <option value="religious">Religious</option>
                              <option value="company">Company</option>
                              <option value="other">Other</option>
                            </select>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Description
                          </label>
                          <textarea
                            value={holidayForm.description}
                            onChange={(e) => setHolidayForm(prev => ({ ...prev, description: e.target.value }))}
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={holidayForm.isRecurring}
                            onChange={(e) => setHolidayForm(prev => ({ ...prev, isRecurring: e.target.checked }))}
                            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          />
                          <label className="text-sm text-gray-700 dark:text-gray-300">Recurring annually</label>
                        </div>
                        <div className="flex justify-end space-x-3 pt-4">
                          <button
                            type="button"
                            onClick={() => setShowHolidayForm(false)}
                            className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                          >
                            Cancel
                          </button>
                          <button
                            type="submit"
                            className="px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors"
                          >
                            Add Holiday
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                )}

                {/* Day-off Form Modal */}
                {showDayOffForm && (
                  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-md">
                      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Add Employee Day-off</h3>
                        <button
                          onClick={() => setShowDayOffForm(false)}
                          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                      <form onSubmit={handleAddDayOff} className="p-6 space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Employee
                          </label>
                          <select
                            value={dayOffForm.employeeId}
                            onChange={(e) => setDayOffForm(prev => ({ ...prev, employeeId: e.target.value }))}
                            required
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          >
                            <option value="">Select employee</option>
                            {employees.filter(emp => emp.status === 'active').map(employee => (
                              <option key={employee.id} value={employee.id}>{employee.name}</option>
                            ))}
                          </select>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              Date
                            </label>
                            <input
                              type="date"
                              value={dayOffForm.date}
                              onChange={(e) => setDayOffForm(prev => ({ ...prev, date: e.target.value }))}
                              required
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              Type
                            </label>
                            <select
                              value={dayOffForm.type}
                              onChange={(e) => setDayOffForm(prev => ({ ...prev, type: e.target.value as any }))}
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            >
                              <option value="vacation">Vacation</option>
                              <option value="sick">Sick Leave</option>
                              <option value="personal">Personal</option>
                              <option value="emergency">Emergency</option>
                              <option value="other">Other</option>
                            </select>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Reason
                          </label>
                          <input
                            type="text"
                            value={dayOffForm.reason}
                            onChange={(e) => setDayOffForm(prev => ({ ...prev, reason: e.target.value }))}
                            required
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Notes
                          </label>
                          <textarea
                            value={dayOffForm.notes}
                            onChange={(e) => setDayOffForm(prev => ({ ...prev, notes: e.target.value }))}
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                        <div className="flex justify-end space-x-3 pt-4">
                          <button
                            type="button"
                            onClick={() => setShowDayOffForm(false)}
                            className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                          >
                            Cancel
                          </button>
                          <button
                            type="submit"
                            className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                          >
                            Add Day-off
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'attendance' && (
            <div className="space-y-8">
              {/* Late Arrival Penalty */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Late Arrival Penalty</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">Amount deducted from salary for excessive late arrivals</p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Number of late days for penalty
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={formData.attendanceRules.lateDaysForPenalty}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        attendanceRules: { ...prev.attendanceRules, lateDaysForPenalty: parseInt(e.target.value) || 1 }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Penalty amount ($)
                    </label>
                    <input
                      type="number"
                      value={formData.attendanceRules.latePenalty}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        attendanceRules: { ...prev.attendanceRules, latePenalty: parseFloat(e.target.value) || 0 }
                      }))}
                      min="0"
                      step="0.01"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Allowance time after start time (minutes)
                  </label>
                  <input
                    type="number"
                    value={formData.attendanceRules.allowanceTime}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      attendanceRules: { ...prev.attendanceRules, allowanceTime: parseInt(e.target.value) || 0 }
                    }))}
                    min="0"
                    className="w-full md:w-64 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                  <p className="text-xs text-primary-600 dark:text-primary-400 mt-2">
                    Employees arriving within {formData.attendanceRules.allowanceTime} minutes after {formData.workingHours.start} will not be considered late.
                  </p>
                </div>
              </div>

              {/* Absence Penalty */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Absence Penalty</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">Amount deducted from salary for excessive absences</p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Number of absent days for penalty
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={formData.attendanceRules.absentDaysForPenalty}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        attendanceRules: { ...prev.attendanceRules, absentDaysForPenalty: parseInt(e.target.value) || 1 }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Penalty amount ($)
                    </label>
                    <input
                      type="number"
                      value={formData.attendanceRules.absencePenalty}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        attendanceRules: { ...prev.attendanceRules, absencePenalty: parseFloat(e.target.value) || 0 }
                      }))}
                      min="0"
                      step="0.01"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                </div>

                <p className="text-xs text-primary-600 dark:text-primary-400 mt-4">
                  Absences during holidays and non-working days will not count towards penalties.
                </p>
              </div>

              {/* Overtime Reward */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Overtime Reward</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">Additional compensation for working beyond standard hours</p>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Reward per hour of overtime ($)
                  </label>
                  <input
                    type="number"
                    value={formData.attendanceRules.overtimeReward}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      attendanceRules: { ...prev.attendanceRules, overtimeReward: parseFloat(e.target.value) || 0 }
                    }))}
                    min="0"
                    step="0.01"
                    className="w-full md:w-64 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                  <p className="text-xs text-primary-600 dark:text-primary-400 mt-2">
                    Employees working after {formData.workingHours.end} will receive ${formData.attendanceRules.overtimeReward} per hour as overtime compensation.
                  </p>
                </div>
              </div>

              {/* Rules Summary */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Rules Summary</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Late penalty after {formData.attendanceRules.lateDaysForPenalty} days:</span>
                    <span className="font-medium text-gray-900 dark:text-white">${formData.attendanceRules.latePenalty}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Absence penalty after {formData.attendanceRules.absentDaysForPenalty} days:</span>
                    <span className="font-medium text-gray-900 dark:text-white">${formData.attendanceRules.absencePenalty}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Allowance time:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{formData.attendanceRules.allowanceTime} minutes</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Overtime reward:</span>
                    <span className="font-medium text-gray-900 dark:text-white">${formData.attendanceRules.overtimeReward}/hour</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Working hours:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{formData.workingHours.start} - {formData.workingHours.end}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Working days:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {formData.workingDays.map(day => daysOfWeek.find(d => d.id === day)?.short).join(', ')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'layout' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Layout Preferences</h3>
                
                <div className="space-y-6">
                  {/* Primary Color Picker */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      Primary Color
                    </label>
                    <div className="grid grid-cols-4 md:grid-cols-7 gap-3">
                      {primaryColors.map((color) => (
                        <button
                          key={color.id}
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, primaryColor: color.id }))}
                          className={`
                            relative p-3 rounded-lg border-2 transition-all duration-200 hover:scale-105
                            ${formData.primaryColor === color.id 
                              ? 'border-gray-900 dark:border-white shadow-lg' 
                              : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                            }
                          `}
                        >
                          <div 
                            className="w-8 h-8 rounded-full mx-auto"
                            style={{ backgroundColor: color.color }}
                          ></div>
                          <span className="text-xs text-gray-700 dark:text-gray-300 mt-2 block">{color.name}</span>
                          {formData.primaryColor === color.id && (
                            <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs">✓</span>
                            </div>
                          )}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Language Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Language
                    </label>
                    <div className="flex space-x-4">
                      <button
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, language: 'en', rtlMode: false }))}
                        className={`
                          flex items-center space-x-3 p-4 border-2 rounded-lg transition-all duration-200
                          ${formData.language === 'en' 
                            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' 
                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                          }
                        `}
                      >
                        <span className="text-2xl">🇺🇸</span>
                        <div className="text-left">
                          <div className="font-medium text-gray-900 dark:text-white">English</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">Left to Right</div>
                        </div>
                      </button>
                      
                      <button
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, language: 'ar', rtlMode: true }))}
                        className={`
                          flex items-center space-x-3 p-4 border-2 rounded-lg transition-all duration-200
                          ${formData.language === 'ar' 
                            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' 
                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                          }
                        `}
                      >
                        <span className="text-2xl">🇪🇬</span>
                        <div className="text-left">
                          <div className="font-medium text-gray-900 dark:text-white">العربية</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">Right to Left</div>
                        </div>
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Currency
                      </label>
                      <select
                        value={formData.currency}
                        onChange={(e) => setFormData(prev => ({ ...prev, currency: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="USD">US Dollar (USD)</option>
                        <option value="EGP">Egyptian Pound (EGP)</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Timezone
                      </label>
                      <select
                        value={formData.timezone}
                        onChange={(e) => setFormData(prev => ({ ...prev, timezone: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">Eastern Time</option>
                        <option value="America/Chicago">Central Time</option>
                        <option value="America/Denver">Mountain Time</option>
                        <option value="America/Los_Angeles">Pacific Time</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">Dark Mode</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Switch between light and dark themes</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.darkMode}
                          onChange={(e) => setFormData(prev => ({ ...prev, darkMode: e.target.checked }))}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">RTL Mode</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Enable right-to-left layout (automatically enabled for Arabic)</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.rtlMode}
                          onChange={(e) => setFormData(prev => ({ ...prev, rtlMode: e.target.checked }))}
                          disabled={formData.language === 'ar'}
                          className="sr-only peer disabled:opacity-50"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600 peer-disabled:opacity-50"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}