import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Clock, 
  Users, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  FileText,
  UserCheck,
  UserX,
  BarChart3,
  ChevronLeft,
  ChevronRight,
  Download,
  Edit,
  Eye,
  Zap
} from 'lucide-react';
import { format, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, parseISO } from 'date-fns';
import { useEmployees, useAttendance, useSettings, useHolidays, useEmployeeDayOffs } from '../../hooks/useDatabase';
import { Employee, AttendanceRecord } from '../../types';
import { 
  MarkAttendanceModal, 
  BulkCheckInModal, 
  EditAttendanceModal, 
  MarkLatePenaltyModal 
} from './AttendanceModals';

export default function AttendanceList() {
  const { employees } = useEmployees();
  const { attendance, addAttendanceRecord, updateAttendanceRecord } = useAttendance();
  const { settings } = useSettings(true);
  const { holidays } = useHolidays();
  const { dayOffs } = useEmployeeDayOffs();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedEmployee, setSelectedEmployee] = useState<string>('all');
  const [todayAttendance, setTodayAttendance] = useState<(AttendanceRecord & { employee: Employee })[]>([]);
  const [autoMarkingProcessed, setAutoMarkingProcessed] = useState(false);

  // Modal states
  const [showMarkAttendanceModal, setShowMarkAttendanceModal] = useState(false);
  const [showBulkCheckInModal, setShowBulkCheckInModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showLatePenaltyModal, setShowLatePenaltyModal] = useState(false);
  const [editingRecord, setEditingRecord] = useState<(AttendanceRecord & { employee: Employee }) | null>(null);

  const selectedDateString = format(selectedDate, 'yyyy-MM-dd');

  // Check if a date is a holiday
  const isHoliday = (date: string) => {
    return holidays.some(holiday => holiday.date === date);
  };

  // Check if an employee has a day-off on a specific date
  const hasEmployeeDayOff = (employeeId: string, date: string) => {
    return dayOffs.some(dayOff => 
      dayOff.employeeId === employeeId && 
      dayOff.date === date && 
      dayOff.status === 'approved'
    );
  };

  // Smart attendance status calculation with holidays and day-offs
  const calculateAttendanceStatus = (
    checkIn?: string, 
    checkOut?: string, 
    date?: string,
    employeeId?: string
  ): 'present' | 'late' | 'absent' | 'overtime' => {
    if (!settings) return 'absent';

    const currentDate = date || selectedDateString;
    const dayOfWeek = format(new Date(currentDate), 'EEEE').toLowerCase();
    
    // Check if it's a holiday
    if (isHoliday(currentDate)) {
      return checkIn ? 'present' : 'absent'; // Don't mark absent on holidays
    }

    // Check if employee has a day-off
    if (employeeId && hasEmployeeDayOff(employeeId, currentDate)) {
      return checkIn ? 'present' : 'absent'; // Don't mark absent on approved day-offs
    }

    // Check if it's a working day
    const isWorkingDay = settings.workingDays.includes(dayOfWeek);
    
    if (!isWorkingDay) {
      return checkIn ? 'present' : 'absent';
    }

    // If no check-in on a working day (and not holiday/day-off), mark as absent
    if (!checkIn) {
      return 'absent';
    }

    // Calculate if late - BUT NOT if it's a holiday or day-off
    const [workStartHour, workStartMin] = settings.workingHours.start.split(':').map(Number);
    const [checkInHour, checkInMin] = checkIn.split(':').map(Number);
    
    const workStartMinutes = workStartHour * 60 + workStartMin;
    const checkInMinutes = checkInHour * 60 + checkInMin;
    const lateMinutes = checkInMinutes - workStartMinutes;
    
    // Don't mark as late if it's a holiday or employee has day-off
    if (isHoliday(currentDate) || (employeeId && hasEmployeeDayOff(employeeId, currentDate))) {
      // Skip late calculation for holidays and day-offs
    } else {
      // Check if late (beyond allowance time) only on regular working days
      if (lateMinutes > settings.attendanceRules.allowanceTime) {
        return 'late';
      }
    }

    // Check for overtime (if checked out after work end time)
    if (checkOut) {
      const [workEndHour, workEndMin] = settings.workingHours.end.split(':').map(Number);
      const [checkOutHour, checkOutMin] = checkOut.split(':').map(Number);
      
      const workEndMinutes = workEndHour * 60 + workEndMin;
      const checkOutMinutes = checkOutHour * 60 + checkOutMin;
      
      if (checkOutMinutes > workEndMinutes) {
        return 'overtime';
      }
    }

    return 'present';
  };

  // Auto-update attendance status when records change
  useEffect(() => {
    const updateAttendanceStatuses = async () => {
      if (!settings) return;

      for (const record of attendance) {
        const calculatedStatus = calculateAttendanceStatus(
          record.checkIn, 
          record.checkOut, 
          record.date, 
          record.employeeId
        );
        
        if (record.status !== calculatedStatus) {
          await updateAttendanceRecord(record.id, { status: calculatedStatus });
        }
      }
    };

    updateAttendanceStatuses();
  }, [attendance, settings, holidays, dayOffs, updateAttendanceRecord]);

  useEffect(() => {
    const todayRecords = attendance.filter(record => record.date === selectedDateString);
    const recordsWithEmployees = todayRecords.map(record => ({
      ...record,
      employee: employees.find(emp => emp.id === record.employeeId)!
    })).filter(record => record.employee);

    setTodayAttendance(recordsWithEmployees);
  }, [attendance, employees, selectedDateString]);

  // FIXED: Auto-mark absent employees for working days (excluding holidays and day-offs)
  useEffect(() => {
    const autoMarkAbsent = async () => {
      if (!settings || autoMarkingProcessed) return;

      const currentDate = new Date();
      const today = format(currentDate, 'yyyy-MM-dd');
      const dayOfWeek = format(currentDate, 'EEEE').toLowerCase();
      
      // Only process if we're viewing today's date
      if (selectedDateString !== today) return;
      
      // Don't auto-mark on holidays
      if (isHoliday(today)) return;
      
      // Only auto-mark on working days and after allowance time has passed
      if (!settings.workingDays.includes(dayOfWeek)) return;
      
      const [workStartHour, workStartMin] = settings.workingHours.start.split(':').map(Number);
      const allowanceMinutes = settings.attendanceRules.allowanceTime;
      const currentHour = currentDate.getHours();
      const currentMinutes = currentDate.getMinutes();
      const currentTotalMinutes = currentHour * 60 + currentMinutes;
      const workStartTotalMinutes = workStartHour * 60 + workStartMin;
      const allowanceEndTime = workStartTotalMinutes + allowanceMinutes;
      
      // Only run after allowance time has passed
      if (currentTotalMinutes < allowanceEndTime) return;

      const activeEmployees = employees.filter(emp => emp.status === 'active');
      let markedAnyAbsent = false;
      
      for (const employee of activeEmployees) {
        // Skip if employee has approved day-off
        if (hasEmployeeDayOff(employee.id, today)) continue;

        const existingRecord = attendance.find(
          record => record.employeeId === employee.id && record.date === today
        );

        // If no record exists for a working day (and not holiday/day-off), mark as absent
        if (!existingRecord) {
          await addAttendanceRecord({
            employeeId: employee.id,
            date: today,
            status: 'absent'
          });
          markedAnyAbsent = true;
        }
      }

      // Mark as processed to prevent multiple runs
      if (markedAnyAbsent || activeEmployees.length > 0) {
        setAutoMarkingProcessed(true);
      }
    };

    // Only run auto-mark absent check once per session for today
    autoMarkAbsent();
  }, [employees, attendance, settings, holidays, dayOffs, addAttendanceRecord, selectedDateString, autoMarkingProcessed]);

  // Reset auto-marking when date changes
  useEffect(() => {
    setAutoMarkingProcessed(false);
  }, [selectedDateString]);

  const handleMarkAttendance = async (employeeId: string, checkIn: string, checkOut?: string) => {
    const status = calculateAttendanceStatus(checkIn, checkOut, selectedDateString, employeeId);
    
    const existingRecord = attendance.find(
      record => record.employeeId === employeeId && record.date === selectedDateString
    );

    if (existingRecord) {
      await updateAttendanceRecord(existingRecord.id, {
        checkIn,
        checkOut,
        status
      });
    } else {
      await addAttendanceRecord({
        employeeId,
        date: selectedDateString,
        checkIn,
        checkOut,
        status
      });
    }
  };

  const handleBulkCheckIn = async (employeeIds: string[]) => {
    const now = new Date();
    const timeString = format(now, 'HH:mm');
    
    for (const employeeId of employeeIds) {
      const existingRecord = attendance.find(
        record => record.employeeId === employeeId && record.date === selectedDateString
      );

      const status = calculateAttendanceStatus(timeString, undefined, selectedDateString, employeeId);

      if (!existingRecord) {
        await addAttendanceRecord({
          employeeId,
          date: selectedDateString,
          checkIn: timeString,
          status
        });
      }
    }
  };

  const handleEditRecord = (record: AttendanceRecord & { employee: Employee }) => {
    setEditingRecord(record);
    setShowEditModal(true);
  };

  const handleUpdateRecord = async (recordId: string, updates: Partial<AttendanceRecord>) => {
    // Recalculate status if check-in or check-out times are updated
    if (updates.checkIn || updates.checkOut) {
      const record = attendance.find(r => r.id === recordId);
      if (record) {
        const newCheckIn = updates.checkIn || record.checkIn;
        const newCheckOut = updates.checkOut || record.checkOut;
        updates.status = calculateAttendanceStatus(
          newCheckIn, 
          newCheckOut, 
          record.date, 
          record.employeeId
        );
      }
    }

    await updateAttendanceRecord(recordId, updates);
    setShowEditModal(false);
    setEditingRecord(null);
  };

  const handleApplyLatePenalty = async (employeeIds: string[], reason: string, amount: number) => {
    for (const employeeId of employeeIds) {
      const existingRecord = attendance.find(
        record => record.employeeId === employeeId && record.date === selectedDateString
      );

      if (existingRecord) {
        await updateAttendanceRecord(existingRecord.id, {
          notes: (existingRecord.notes || '') + ` Manual penalty applied: $${amount}. Reason: ${reason}`
        });
      }
    }
    
    alert(`Manual penalty applied to ${employeeIds.length} employees.`);
  };

  const handleGenerateReport = () => {
    const reportData = {
      date: selectedDateString,
      totalEmployees: employees.filter(emp => emp.status === 'active').length,
      present: todayAttendance.filter(a => a.status === 'present').length,
      absent: todayAttendance.filter(a => a.status === 'absent').length,
      late: todayAttendance.filter(a => a.status === 'late').length,
      overtime: todayAttendance.filter(a => a.status === 'overtime').length,
      records: todayAttendance
    };
    
    console.log('Attendance Report:', reportData);
    alert('Report generated! Check console for details.');
  };

  const handleCheckIn = async (employeeId: string) => {
    const now = new Date();
    const timeString = format(now, 'HH:mm');
    const status = calculateAttendanceStatus(timeString, undefined, selectedDateString, employeeId);
    
    const existingRecord = attendance.find(
      record => record.employeeId === employeeId && record.date === selectedDateString
    );

    if (existingRecord) {
      await updateAttendanceRecord(existingRecord.id, {
        checkIn: timeString,
        status
      });
    } else {
      await addAttendanceRecord({
        employeeId,
        date: selectedDateString,
        checkIn: timeString,
        status
      });
    }
  };

  const handleCheckOut = async (employeeId: string) => {
    const now = new Date();
    const timeString = format(now, 'HH:mm');
    
    const existingRecord = attendance.find(
      record => record.employeeId === employeeId && record.date === selectedDateString
    );

    if (existingRecord) {
      const status = calculateAttendanceStatus(
        existingRecord.checkIn, 
        timeString, 
        selectedDateString, 
        employeeId
      );
      await updateAttendanceRecord(existingRecord.id, {
        checkOut: timeString,
        status
      });
    }
  };

  const markAbsent = async (employeeId: string) => {
    const existingRecord = attendance.find(
      record => record.employeeId === employeeId && record.date === selectedDateString
    );

    if (existingRecord) {
      await updateAttendanceRecord(existingRecord.id, {
        status: 'absent'
      });
    } else {
      await addAttendanceRecord({
        employeeId,
        date: selectedDateString,
        status: 'absent'
      });
    }
  };

  const getAttendanceStatus = (employeeId: string) => {
    return attendance.find(
      record => record.employeeId === employeeId && record.date === selectedDateString
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <CheckCircle className="h-4 w-4 text-emerald-600" />;
      case 'absent':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'late':
        return <AlertCircle className="h-4 w-4 text-amber-600" />;
      case 'overtime':
        return <Zap className="h-4 w-4 text-purple-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'present':
        return <span className="px-2 py-1 bg-emerald-100 text-emerald-800 text-xs rounded-full font-medium">Present</span>;
      case 'late':
        return <span className="px-2 py-1 bg-amber-100 text-amber-800 text-xs rounded-full font-medium">Late</span>;
      case 'absent':
        return <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full font-medium">Absent</span>;
      case 'overtime':
        return <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full font-medium">Overtime</span>;
      default:
        return <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full font-medium">-</span>;
    }
  };

  const calculateWorkingHours = (checkIn?: string, checkOut?: string) => {
    if (!checkIn || !checkOut) return 'N/A';
    
    const [inHour, inMin] = checkIn.split(':').map(Number);
    const [outHour, outMin] = checkOut.split(':').map(Number);
    
    const inMinutes = inHour * 60 + inMin;
    const outMinutes = outHour * 60 + outMin;
    
    const diffMinutes = outMinutes - inMinutes;
    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;
    
    return `${hours}h ${minutes}m`;
  };

  const calculateLateMinutes = (checkIn?: string) => {
    if (!checkIn || !settings) return 0;
    
    const [hour, min] = checkIn.split(':').map(Number);
    const checkInMinutes = hour * 60 + min;
    const [workStartHour, workStartMin] = settings.workingHours.start.split(':').map(Number);
    const workStartMinutes = workStartHour * 60 + workStartMin;
    
    return Math.max(0, checkInMinutes - workStartMinutes - settings.attendanceRules.allowanceTime);
  };

  const calculateOvertimeHours = (checkOut?: string) => {
    if (!checkOut || !settings) return 0;
    
    const [hour, min] = checkOut.split(':').map(Number);
    const checkOutMinutes = hour * 60 + min;
    const [workEndHour, workEndMin] = settings.workingHours.end.split(':').map(Number);
    const workEndMinutes = workEndHour * 60 + workEndMin;
    
    const overtimeMinutes = Math.max(0, checkOutMinutes - workEndMinutes);
    return Math.round(overtimeMinutes / 60 * 100) / 100; // Round to 2 decimal places
  };

  const activeEmployees = employees.filter(emp => emp.status === 'active');
  const presentCount = todayAttendance.filter(a => a.status === 'present').length;
  const lateCount = todayAttendance.filter(a => a.status === 'late').length;
  const absentCount = todayAttendance.filter(a => a.status === 'absent').length;
  const overtimeCount = todayAttendance.filter(a => a.status === 'overtime').length;
  const lateEmployees = todayAttendance.filter(a => a.status === 'late');

  // Calendar generation
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const calendarDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  const daysOfWeek = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

  // Check if selected date is a working day, holiday, or has day-offs
  const selectedDayOfWeek = format(selectedDate, 'EEEE').toLowerCase();
  const isWorkingDay = settings?.workingDays.includes(selectedDayOfWeek) || false;
  const isSelectedDateHoliday = isHoliday(selectedDateString);
  const selectedDateDayOffs = dayOffs.filter(dayOff => dayOff.date === selectedDateString);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Smart Attendance Management</h1>
          <div className="flex items-center space-x-4 mt-1">
            {!isWorkingDay && !isSelectedDateHoliday && (
              <p className="text-sm text-amber-600 dark:text-amber-400">
                ⚠️ {format(selectedDate, 'EEEE')} is not a working day
              </p>
            )}
            {isSelectedDateHoliday && (
              <p className="text-sm text-blue-600 dark:text-blue-400">
                🎉 Holiday: {holidays.find(h => h.date === selectedDateString)?.name}
              </p>
            )}
            {selectedDateDayOffs.length > 0 && (
              <p className="text-sm text-green-600 dark:text-green-400">
                📅 {selectedDateDayOffs.length} employee(s) on day-off
              </p>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleGenerateReport}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Download className="h-4 w-4" />
            <span>Export Report</span>
          </button>
          <button
            onClick={() => setShowMarkAttendanceModal(true)}
            className="flex items-center space-x-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors"
          >
            <UserCheck className="h-4 w-4" />
            <span>Mark Attendance</span>
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-emerald-600">{presentCount}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Present Today</p>
              <p className="text-xs text-gray-500">Out of {activeEmployees.length} employees</p>
            </div>
            <CheckCircle className="h-8 w-8 text-emerald-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-amber-600">{lateCount}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Late Arrivals</p>
              <p className="text-xs text-gray-500">Penalty applicable</p>
            </div>
            <AlertCircle className="h-8 w-8 text-amber-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-red-600">{absentCount}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Absent</p>
              <p className="text-xs text-gray-500">Auto-marked after allowance time</p>
            </div>
            <XCircle className="h-8 w-8 text-red-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-purple-600">{overtimeCount}</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Overtime</p>
              <p className="text-xs text-gray-500">Extra hours worked</p>
            </div>
            <Zap className="h-8 w-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-blue-600">
                {settings ? `${settings.workingHours.start}-${settings.workingHours.end}` : 'N/A'}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Working Hours</p>
              <p className="text-xs text-gray-500">±{settings?.attendanceRules.allowanceTime || 0}min allowance</p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-600" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Calendar Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Smart Calendar</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">View attendance with holidays and day-offs</p>
          </div>

          {/* Calendar Header */}
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => setCurrentMonth(subMonths(currentMonth, 1))}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <h3 className="font-semibold text-gray-900 dark:text-white">
              {format(currentMonth, 'MMMM yyyy')}
            </h3>
            <button
              onClick={() => setCurrentMonth(addMonths(currentMonth, 1))}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {daysOfWeek.map(day => (
              <div key={day} className="text-center text-xs font-medium text-gray-500 p-2">
                {day}
              </div>
            ))}
          </div>

          <div className="grid grid-cols-7 gap-1">
            {calendarDays.map(day => {
              const isSelected = isSameDay(day, selectedDate);
              const isToday = isSameDay(day, new Date());
              const dayOfWeek = format(day, 'EEEE').toLowerCase();
              const isDayWorkingDay = settings?.workingDays.includes(dayOfWeek) || false;
              const dayString = format(day, 'yyyy-MM-dd');
              const isDayHoliday = isHoliday(dayString);
              const dayDayOffs = dayOffs.filter(dayOff => dayOff.date === dayString);
              
              return (
                <button
                  key={day.toISOString()}
                  onClick={() => setSelectedDate(day)}
                  className={`
                    p-2 text-sm rounded-lg transition-colors relative
                    ${isSelected 
                      ? 'bg-gray-900 text-white' 
                      : isToday 
                        ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                        : isDayHoliday
                          ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/50'
                          : isDayWorkingDay
                            ? 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                            : 'text-gray-400 dark:text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
                    }
                  `}
                >
                  {format(day, 'd')}
                  {/* Indicators */}
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex space-x-1">
                    {isDayHoliday && (
                      <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                    )}
                    {dayDayOffs.length > 0 && (
                      <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                    )}
                    {!isDayWorkingDay && !isDayHoliday && (
                      <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                    )}
                  </div>
                </button>
              );
            })}
          </div>

          {/* Legend */}
          <div className="mt-4 space-y-2 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-gray-600 dark:text-gray-400">Holiday</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-gray-600 dark:text-gray-400">Day-off</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              <span className="text-gray-600 dark:text-gray-400">Non-working day</span>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-6 space-y-3">
            <h3 className="font-medium text-gray-900 dark:text-white">Quick Actions</h3>
            <button
              onClick={() => setShowBulkCheckInModal(true)}
              className="w-full flex items-center space-x-2 p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <UserCheck className="h-4 w-4" />
              <span className="text-sm">Bulk Check-in</span>
            </button>
            <button
              onClick={() => setShowLatePenaltyModal(true)}
              className="w-full flex items-center space-x-2 p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <UserX className="h-4 w-4" />
              <span className="text-sm">Mark Manual Penalty</span>
            </button>
            <button
              onClick={handleGenerateReport}
              className="w-full flex items-center space-x-2 p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <BarChart3 className="h-4 w-4" />
              <span className="text-sm">Generate Report</span>
            </button>
          </div>
        </div>

        {/* Attendance Records */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Smart Attendance Records</h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Records for {format(selectedDate, 'M/d/yyyy')} 
                  {isWorkingDay && !isSelectedDateHoliday ? ' (Working Day)' : 
                   isSelectedDateHoliday ? ' (Holiday)' : ' (Non-Working Day)'}
                </p>
              </div>
              <select
                value={selectedEmployee}
                onChange={(e) => setSelectedEmployee(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              >
                <option value="all">All Employees</option>
                {activeEmployees.map(emp => (
                  <option key={emp.id} value={emp.id}>{emp.name}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Table Header */}
          <div className="grid grid-cols-7 gap-4 p-4 border-b border-gray-200 dark:border-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400">
            <div>Employee</div>
            <div>Check In</div>
            <div>Check Out</div>
            <div>Status</div>
            <div>Hours</div>
            <div>Late/OT</div>
            <div>Actions</div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {activeEmployees
              .filter(emp => selectedEmployee === 'all' || emp.id === selectedEmployee)
              .map((employee) => {
                const attendanceRecord = getAttendanceStatus(employee.id);
                const lateMinutes = attendanceRecord?.checkIn ? calculateLateMinutes(attendanceRecord.checkIn) : 0;
                const overtimeHours = attendanceRecord?.checkOut ? calculateOvertimeHours(attendanceRecord.checkOut) : 0;
                const recordWithEmployee = attendanceRecord ? { ...attendanceRecord, employee } : null;
                
                // Check if employee has day-off or it's a holiday
                const hasEmployeeDayOffToday = hasEmployeeDayOff(employee.id, selectedDateString);
                const isHolidayToday = isHoliday(selectedDateString);
                
                return (
                  <div key={employee.id} className="grid grid-cols-7 gap-4 p-4 items-center text-sm">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 dark:text-blue-400 font-medium text-xs">
                          {employee.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-900 dark:text-white">{employee.name}</span>
                        {(hasEmployeeDayOffToday || isHolidayToday) && (
                          <div className="text-xs text-blue-600 dark:text-blue-400">
                            {isHolidayToday ? 'Holiday' : 'Day-off'}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="text-gray-900 dark:text-white">
                      {attendanceRecord?.checkIn || '-'}
                      {lateMinutes > 0 && !hasEmployeeDayOffToday && !isHolidayToday && (
                        <div className="text-xs text-red-600">+{lateMinutes}min late</div>
                      )}
                    </div>
                    
                    <div className="text-gray-900 dark:text-white">
                      {attendanceRecord?.checkOut || '-'}
                      {overtimeHours > 0 && (
                        <div className="text-xs text-purple-600">+{overtimeHours}h OT</div>
                      )}
                    </div>
                    
                    <div>
                      {attendanceRecord ? getStatusBadge(attendanceRecord.status) : getStatusBadge('pending')}
                    </div>
                    
                    <div className="text-gray-900 dark:text-white">
                      {calculateWorkingHours(attendanceRecord?.checkIn, attendanceRecord?.checkOut)}
                    </div>
                    
                    <div className="text-sm">
                      {lateMinutes > 0 && !hasEmployeeDayOffToday && !isHolidayToday && (
                        <div className="text-red-600">Late: {lateMinutes}m</div>
                      )}
                      {overtimeHours > 0 && (
                        <div className="text-purple-600">OT: {overtimeHours}h</div>
                      )}
                      {(lateMinutes === 0 || hasEmployeeDayOffToday || isHolidayToday) && overtimeHours === 0 && '-'}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {!attendanceRecord?.checkIn ? (
                        <button
                          onClick={() => handleCheckIn(employee.id)}
                          className="px-2 py-1 bg-emerald-600 text-white text-xs rounded hover:bg-emerald-700 transition-colors"
                        >
                          Check In
                        </button>
                      ) : (
                        !attendanceRecord.checkOut && (
                          <button
                            onClick={() => handleCheckOut(employee.id)}
                            className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                          >
                            Check Out
                          </button>
                        )
                      )}
                      
                      {recordWithEmployee && (
                        <button 
                          onClick={() => handleEditRecord(recordWithEmployee)}
                          className="p-1 text-gray-600 hover:text-blue-600 transition-colors"
                        >
                          <Edit className="h-3 w-3" />
                        </button>
                      )}
                      <button className="p-1 text-gray-600 hover:text-green-600 transition-colors">
                        <Eye className="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                );
              })}
          </div>

          {activeEmployees.length === 0 && (
            <div className="p-12 text-center">
              <p className="text-gray-500 dark:text-gray-400">No active employees found</p>
            </div>
          )}
        </div>
      </div>

      {/* Smart Attendance Rules Summary */}
      {settings && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Smart Attendance Rules</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-medium text-gray-900 dark:text-white">Working Hours</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {settings.workingHours.start} - {settings.workingHours.end}
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-400">
                ±{settings.attendanceRules.allowanceTime}min allowance
              </p>
            </div>
            
            <div className="text-center p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
              <AlertCircle className="h-8 w-8 text-amber-600 mx-auto mb-2" />
              <h3 className="font-medium text-gray-900 dark:text-white">Late Penalty</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                ${settings.attendanceRules.latePenalty}/day
              </p>
              <p className="text-xs text-amber-600 dark:text-amber-400">
                After {settings.attendanceRules.lateDaysForPenalty || 3} late days
              </p>
            </div>
            
            <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <XCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
              <h3 className="font-medium text-gray-900 dark:text-white">Absence Penalty</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                ${settings.attendanceRules.absencePenalty}/day
              </p>
              <p className="text-xs text-red-600 dark:text-red-400">
                After {settings.attendanceRules.absentDaysForPenalty || 2} absent days
              </p>
            </div>
            
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <Zap className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-medium text-gray-900 dark:text-white">Overtime Bonus</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                ${settings.attendanceRules.overtimeReward}/hour
              </p>
              <p className="text-xs text-purple-600 dark:text-purple-400">
                After {settings.workingHours.end}
              </p>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <h4 className="font-medium text-green-800 dark:text-green-300 mb-2">Smart Features</h4>
            <ul className="text-sm text-green-700 dark:text-green-400 space-y-1">
              <li>• Automatically excludes holidays from absence penalties</li>
              <li>• Respects individual employee day-offs</li>
              <li>• Real-time status calculation based on working hours</li>
              <li>• Penalties only apply after threshold days are reached</li>
              <li>• Visual calendar with holiday and day-off indicators</li>
              <li>• Manual penalty application through "Mark Manual Penalty" button</li>
              <li>• Auto-absence marking after allowance time passes</li>
              <li>• Holiday/day-off workers are not marked as "late"</li>
            </ul>
          </div>
        </div>
      )}

      {/* Modals */}
      <MarkAttendanceModal
        isOpen={showMarkAttendanceModal}
        onClose={() => setShowMarkAttendanceModal(false)}
        employees={activeEmployees}
        onMarkAttendance={handleMarkAttendance}
      />

      <BulkCheckInModal
        isOpen={showBulkCheckInModal}
        onClose={() => setShowBulkCheckInModal(false)}
        employees={activeEmployees}
        onBulkCheckIn={handleBulkCheckIn}
      />

      <EditAttendanceModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        record={editingRecord}
        onUpdateRecord={handleUpdateRecord}
      />

      <MarkLatePenaltyModal
        isOpen={showLatePenaltyModal}
        onClose={() => setShowLatePenaltyModal(false)}
        employees={activeEmployees}
        lateEmployees={lateEmployees}
        onApplyPenalty={handleApplyLatePenalty}
      />
    </div>
  );
}