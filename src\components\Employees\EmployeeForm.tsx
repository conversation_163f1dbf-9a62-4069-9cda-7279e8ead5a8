import React, { useState, useEffect } from 'react';
import { X, User, Trash2, AlertCircle } from 'lucide-react';
import { Employee } from '../../types';
import { useEmployees, useAttendance, useSettings } from '../../hooks/useDatabase';
import { useTranslation } from '../../utils/translations';
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, subWeeks, subMonths, subYears } from 'date-fns';

interface EmployeeFormProps {
  employee?: Employee | null;
  onClose: () => void;
}

export default function EmployeeForm({ employee, onClose }: EmployeeFormProps) {
  const { addEmployee, updateEmployee, deleteEmployee } = useEmployees();
  const { attendance } = useAttendance();
  const { settings } = useSettings(true);
  const t = useTranslation(settings?.language || 'en');
  const [formData, setFormData] = useState({
    name: '',
    position: '',
    salary: '',
    phone: '',
    email: '',
    address: '',
    status: 'active' as 'active' | 'inactive',
    notes: '',
    hireDate: new Date().toISOString().split('T')[0],
    photo: '',
  });

  useEffect(() => {
    if (employee) {
      setFormData({
        name: employee.name,
        position: employee.position,
        salary: employee.salary.toString(),
        phone: employee.phone,
        email: employee.email,
        address: employee.address,
        status: employee.status,
        notes: employee.notes || '',
        hireDate: employee.hireDate,
        photo: employee.photo || '',
      });
    }
  }, [employee]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const employeeData = {
      name: formData.name,
      position: formData.position,
      salary: parseFloat(formData.salary),
      phone: formData.phone,
      email: formData.email,
      address: formData.address,
      status: formData.status,
      notes: formData.notes,
      hireDate: formData.hireDate,
      photo: formData.photo,
    };

    try {
      if (employee) {
        await updateEmployee(employee.id, employeeData);
      } else {
        await addEmployee(employeeData);
      }
      onClose();
    } catch (error) {
      console.error('Error saving employee:', error);
    }
  };

  const handleDelete = async () => {
    if (employee && confirm('Are you sure you want to delete this employee? This action cannot be undone.')) {
      try {
        await deleteEmployee(employee.id);
        onClose();
      } catch (error) {
        console.error('Error deleting employee:', error);
      }
    }
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setFormData(prev => ({ ...prev, photo: event.target?.result as string }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemovePhoto = () => {
    setFormData(prev => ({ ...prev, photo: '' }));
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Calculate late minutes for a given period
  const calculateLateMinutes = (startDate: Date, endDate: Date) => {
    if (!employee || !settings) return 0;

    const employeeAttendance = attendance.filter(record => 
      record.employeeId === employee.id &&
      record.status === 'late' &&
      record.checkIn &&
      new Date(record.date) >= startDate &&
      new Date(record.date) <= endDate
    );

    return employeeAttendance.reduce((total, record) => {
      if (!record.checkIn || !settings) return total;
      
      const [hour, min] = record.checkIn.split(':').map(Number);
      const checkInMinutes = hour * 60 + min;
      const [workStartHour, workStartMin] = settings.workingHours.start.split(':').map(Number);
      const workStartMinutes = workStartHour * 60 + workStartMin;
      
      const lateMinutes = Math.max(0, checkInMinutes - workStartMinutes - settings.attendanceRules.allowanceTime);
      return total + lateMinutes;
    }, 0);
  };

  // Calculate overtime minutes for a given period
  const calculateOvertimeMinutes = (startDate: Date, endDate: Date) => {
    if (!employee || !settings) return 0;

    const employeeAttendance = attendance.filter(record => 
      record.employeeId === employee.id &&
      record.status === 'overtime' &&
      record.checkOut &&
      new Date(record.date) >= startDate &&
      new Date(record.date) <= endDate
    );

    return employeeAttendance.reduce((total, record) => {
      if (!record.checkOut || !settings) return total;
      
      const [hour, min] = record.checkOut.split(':').map(Number);
      const checkOutMinutes = hour * 60 + min;
      const [workEndHour, workEndMin] = settings.workingHours.end.split(':').map(Number);
      const workEndMinutes = workEndHour * 60 + workEndMin;
      
      const overtimeMinutes = Math.max(0, checkOutMinutes - workEndMinutes);
      return total + overtimeMinutes;
    }, 0);
  };

  // Calculate absent days for a given period
  const calculateAbsentDays = (startDate: Date, endDate: Date) => {
    if (!employee || !settings) return 0;

    const employeeAttendance = attendance.filter(record => 
      record.employeeId === employee.id &&
      record.status === 'absent' &&
      new Date(record.date) >= startDate &&
      new Date(record.date) <= endDate
    );

    // Only count absences on working days
    return employeeAttendance.filter(record => {
      const recordDate = new Date(record.date);
      const dayOfWeek = format(recordDate, 'EEEE').toLowerCase();
      return settings.workingDays.includes(dayOfWeek);
    }).length;
  };

  // Get attendance stats for different periods
  const getAttendanceStats = () => {
    if (!employee) return null;

    const now = new Date();
    const today = startOfDay(now);
    const todayEnd = endOfDay(now);
    
    const thisWeekStart = startOfWeek(now);
    const thisWeekEnd = endOfWeek(now);
    
    const thisMonthStart = startOfMonth(now);
    const thisMonthEnd = endOfMonth(now);
    
    const thisYearStart = startOfYear(now);
    const thisYearEnd = endOfYear(now);
    
    const prevWeekStart = startOfWeek(subWeeks(now, 1));
    const prevWeekEnd = endOfWeek(subWeeks(now, 1));
    
    const prevMonthStart = startOfMonth(subMonths(now, 1));
    const prevMonthEnd = endOfMonth(subMonths(now, 1));
    
    const prevYearStart = startOfYear(subYears(now, 1));
    const prevYearEnd = endOfYear(subYears(now, 1));

    return {
      lateMinutes: {
        today: calculateLateMinutes(today, todayEnd),
        thisWeek: calculateLateMinutes(thisWeekStart, thisWeekEnd),
        thisMonth: calculateLateMinutes(thisMonthStart, thisMonthEnd),
        thisYear: calculateLateMinutes(thisYearStart, thisYearEnd),
        previousWeek: calculateLateMinutes(prevWeekStart, prevWeekEnd),
        previousMonth: calculateLateMinutes(prevMonthStart, prevMonthEnd),
        previousYear: calculateLateMinutes(prevYearStart, prevYearEnd),
      },
      overtimeMinutes: {
        today: calculateOvertimeMinutes(today, todayEnd),
        thisWeek: calculateOvertimeMinutes(thisWeekStart, thisWeekEnd),
        thisMonth: calculateOvertimeMinutes(thisMonthStart, thisMonthEnd),
        thisYear: calculateOvertimeMinutes(thisYearStart, thisYearEnd),
        previousWeek: calculateOvertimeMinutes(prevWeekStart, prevWeekEnd),
        previousMonth: calculateOvertimeMinutes(prevMonthStart, prevMonthEnd),
        previousYear: calculateOvertimeMinutes(prevYearStart, prevYearEnd),
      },
      absentDays: {
        today: calculateAbsentDays(today, todayEnd),
        thisWeek: calculateAbsentDays(thisWeekStart, thisWeekEnd),
        thisMonth: calculateAbsentDays(thisMonthStart, thisMonthEnd),
        thisYear: calculateAbsentDays(thisYearStart, thisYearEnd),
        previousWeek: calculateAbsentDays(prevWeekStart, prevWeekEnd),
        previousMonth: calculateAbsentDays(prevMonthStart, prevMonthEnd),
        previousYear: calculateAbsentDays(prevYearStart, prevYearEnd),
      }
    };
  };

  // Calculate performance stats for existing employee
  const getEmployeeStats = () => {
    if (!employee) return null;

    const employeeAttendance = attendance.filter(record => record.employeeId === employee.id);
    const presentDays = employeeAttendance.filter(record => record.status === 'present').length;
    const totalDays = employeeAttendance.length;
    const attendanceRate = totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0;
    
    const lastCheckIn = employeeAttendance
      .filter(record => record.checkIn)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];

    return {
      attendanceRate,
      lastCheckIn: lastCheckIn ? `${lastCheckIn.date} ${lastCheckIn.checkIn}` : 'Never',
      employeeId: '#1',
      hireDate: employee.hireDate,
      status: employee.status,
    };
  };

  const stats = getEmployeeStats();
  const attendanceStats = getAttendanceStats();
  const isRTL = settings?.language === 'ar' || settings?.rtlMode;

  const formatMinutes = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}${t.hours.charAt(0)} ${mins}${t.minutes.charAt(0)}`;
    }
    return `${mins}${t.minutes.charAt(0)}`;
  };

  // Add state for tab management
  const [activeTab, setActiveTab] = useState<'current' | 'previous' | 'comparison'>('current');

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-7xl max-h-[90vh] overflow-y-auto">
        <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {employee ? t.editEmployee : t.addNewEmployee}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Left Column - Basic Info Form */}
            <div className="xl:col-span-1 space-y-6">
              {/* Employee Photo Section */}
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-6">
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center border-4 border-white dark:border-gray-600 shadow-lg overflow-hidden">
                    {formData.photo ? (
                      <img src={formData.photo} alt="Employee" className="w-full h-full object-cover rounded-full" />
                    ) : (
                      <User className="h-10 w-10 text-gray-400" />
                    )}
                  </div>
                  <div className={`flex space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    <label className="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors cursor-pointer">
                      {t.changePhoto}
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handlePhotoUpload}
                        className="hidden"
                      />
                    </label>
                    {formData.photo && (
                      <button
                        type="button"
                        onClick={handleRemovePhoto}
                        className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors border border-gray-300 dark:border-gray-600 rounded-lg"
                      >
                        {t.remove}
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Basic Information Form */}
              <div className="space-y-4">
                <div>
                  <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {t.fullName}
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    placeholder="Emma Johnson"
                    className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {t.position}
                  </label>
                  <input
                    type="text"
                    name="position"
                    value={formData.position}
                    onChange={handleChange}
                    required
                    placeholder="Senior Stylist"
                    className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {t.email}
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      placeholder="<EMAIL>"
                      className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {t.phone}
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="+****************"
                      className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {t.salary}
                    </label>
                    <input
                      type="number"
                      name="salary"
                      value={formData.salary}
                      onChange={handleChange}
                      required
                      min="0"
                      step="0.01"
                      placeholder="2500"
                      className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {t.status}
                    </label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleChange}
                      className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                    >
                      <option value="active">{t.active}</option>
                      <option value="inactive">{t.inactive}</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {t.hireDate}
                  </label>
                  <input
                    type="date"
                    name="hireDate"
                    value={formData.hireDate}
                    onChange={handleChange}
                    className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {t.address}
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {t.notes}
                  </label>
                  <textarea
                    name="notes"
                    value={formData.notes}
                    onChange={handleChange}
                    rows={3}
                    className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                  />
                </div>
              </div>
            </div>

            {/* Right Column - Performance Stats and Employment Info */}
            <div className="xl:col-span-2 space-y-6">
              {employee && stats && (
                <>
                  {/* Performance Stats Section */}
                  <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Performance Stats */}
                      <div>
                        <h3 className={`text-lg font-semibold text-gray-900 dark:text-white mb-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                          Performance Stats
                        </h3>
                        <div className="space-y-4">
                          <div>
                            <div className={`flex justify-between text-sm mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                              <span className="text-gray-600 dark:text-gray-400">Attendance Rate:</span>
                              <span className="font-semibold text-gray-900 dark:text-white">{stats.attendanceRate}%</span>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                style={{ width: `${stats.attendanceRate}%` }}
                              ></div>
                            </div>
                          </div>
                          <div className={`text-sm ${isRTL ? 'text-right' : 'text-left'}`}>
                            <span className="text-gray-600 dark:text-gray-400">Last Check-in:</span>
                            <div className="font-medium text-gray-900 dark:text-white mt-1">
                              {stats.lastCheckIn !== 'Never' ? format(new Date(stats.lastCheckIn), 'yyyy-MM-dd HH:mm a') : 'Never'}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Employment Info */}
                      <div>
                        <h3 className={`text-lg font-semibold text-gray-900 dark:text-white mb-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                          Employment Info
                        </h3>
                        <div className="space-y-3">
                          <div className={`flex justify-between text-sm ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <span className="text-gray-600 dark:text-gray-400">Employee ID:</span>
                            <span className="font-semibold text-gray-900 dark:text-white">{stats.employeeId}</span>
                          </div>
                          <div className={`flex justify-between text-sm ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <span className="text-gray-600 dark:text-gray-400">Hire Date:</span>
                            <span className="font-medium text-gray-900 dark:text-white">
                              {format(new Date(stats.hireDate), 'M/d/yyyy')}
                            </span>
                          </div>
                          <div className={`flex justify-between text-sm ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <span className="text-gray-600 dark:text-gray-400">Status:</span>
                            <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${
                              stats.status === 'active'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                            }`}>
                              {stats.status === 'active' ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Attendance Analytics */}
                  {attendanceStats && (
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden">
                      <div className="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                        <h3 className={`text-lg font-semibold text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}>
                          📊 Attendance Analytics
                        </h3>
                      </div>
                      
                      {/* Tab Navigation */}
                      <div className="border-b border-gray-200 dark:border-gray-700">
                        <nav className="flex px-6">
                          <button
                            type="button"
                            onClick={() => setActiveTab('current')}
                            className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                              activeTab === 'current'
                                ? 'border-blue-600 text-blue-600 dark:text-blue-400 dark:border-blue-400'
                                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                            }`}
                          >
                            Current Period
                          </button>
                          <button
                            type="button"
                            onClick={() => setActiveTab('previous')}
                            className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                              activeTab === 'previous'
                                ? 'border-blue-600 text-blue-600 dark:text-blue-400 dark:border-blue-400'
                                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                            }`}
                          >
                            Previous Period
                          </button>
                          <button
                            type="button"
                            onClick={() => setActiveTab('comparison')}
                            className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                              activeTab === 'comparison'
                                ? 'border-blue-600 text-blue-600 dark:text-blue-400 dark:border-blue-400'
                                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                            }`}
                          >
                            Comparison
                          </button>
                        </nav>
                      </div>

                      <div className="p-6">
                        {/* Current Period Tab */}
                        {activeTab === 'current' && (
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            {/* Today */}
                            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Today</h4>
                              <div className="space-y-2 text-xs">
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Late Minutes:</span>
                                  <span className="font-medium text-amber-600">{formatMinutes(attendanceStats.lateMinutes.today)}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Absent Days:</span>
                                  <span className="font-medium text-red-600">{attendanceStats.absentDays.today}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Present Days:</span>
                                  <span className="font-medium text-green-600">1</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Overtime:</span>
                                  <span className="font-medium text-purple-600">{formatMinutes(attendanceStats.overtimeMinutes.today)}</span>
                                </div>
                                <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <span className="text-gray-700 dark:text-gray-300 font-medium">Attendance Rate:</span>
                                    <span className="font-semibold text-blue-600">100.0%</span>
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">1 of 1 working days</div>
                                </div>
                              </div>
                            </div>

                            {/* This Week */}
                            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">This Week</h4>
                              <div className="space-y-2 text-xs">
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Late Minutes:</span>
                                  <span className="font-medium text-amber-600">{formatMinutes(attendanceStats.lateMinutes.thisWeek)}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Absent Days:</span>
                                  <span className="font-medium text-red-600">{attendanceStats.absentDays.thisWeek}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Present Days:</span>
                                  <span className="font-medium text-green-600">2</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Overtime:</span>
                                  <span className="font-medium text-purple-600">{formatMinutes(attendanceStats.overtimeMinutes.thisWeek)}</span>
                                </div>
                                <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <span className="text-gray-700 dark:text-gray-300 font-medium">Attendance Rate:</span>
                                    <span className="font-semibold text-red-600">40.0%</span>
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">2 of 5 working days</div>
                                </div>
                              </div>
                            </div>

                            {/* This Month */}
                            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">This Month</h4>
                              <div className="space-y-2 text-xs">
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Late Minutes:</span>
                                  <span className="font-medium text-amber-600">{formatMinutes(attendanceStats.lateMinutes.thisMonth)}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Absent Days:</span>
                                  <span className="font-medium text-red-600">{attendanceStats.absentDays.thisMonth}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Present Days:</span>
                                  <span className="font-medium text-green-600">2</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Overtime:</span>
                                  <span className="font-medium text-purple-600">{formatMinutes(attendanceStats.overtimeMinutes.thisMonth)}</span>
                                </div>
                                <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <span className="text-gray-700 dark:text-gray-300 font-medium">Attendance Rate:</span>
                                    <span className="font-semibold text-red-600">9.1%</span>
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">2 of 22 working days</div>
                                </div>
                              </div>
                            </div>

                            {/* This Year */}
                            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">This Year</h4>
                              <div className="space-y-2 text-xs">
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Late Minutes:</span>
                                  <span className="font-medium text-amber-600">{formatMinutes(attendanceStats.lateMinutes.thisYear)}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Absent Days:</span>
                                  <span className="font-medium text-red-600">{attendanceStats.absentDays.thisYear}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Present Days:</span>
                                  <span className="font-medium text-green-600">2</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Overtime:</span>
                                  <span className="font-medium text-purple-600">{formatMinutes(attendanceStats.overtimeMinutes.thisYear)}</span>
                                </div>
                                <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <span className="text-gray-700 dark:text-gray-300 font-medium">Attendance Rate:</span>
                                    <span className="font-semibold text-red-600">0.8%</span>
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">2 of 260 working days</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Previous Period Tab */}
                        {activeTab === 'previous' && (
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                            {/* Previous Week */}
                            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Previous Week</h4>
                              <div className="space-y-2 text-xs">
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Late Minutes:</span>
                                  <span className="font-medium text-gray-500">{formatMinutes(attendanceStats.lateMinutes.previousWeek)}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Absent Days:</span>
                                  <span className="font-medium text-gray-500">{attendanceStats.absentDays.previousWeek}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Present Days:</span>
                                  <span className="font-medium text-gray-500">0</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Overtime:</span>
                                  <span className="font-medium text-gray-500">{formatMinutes(attendanceStats.overtimeMinutes.previousWeek)}</span>
                                </div>
                                <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <span className="text-gray-700 dark:text-gray-300 font-medium">Attendance Rate:</span>
                                    <span className="font-semibold text-gray-500">0.0%</span>
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">0 of 5 working days</div>
                                </div>
                              </div>
                            </div>

                            {/* Previous Month */}
                            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Previous Month</h4>
                              <div className="space-y-2 text-xs">
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Late Minutes:</span>
                                  <span className="font-medium text-gray-500">{formatMinutes(attendanceStats.lateMinutes.previousMonth)}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Absent Days:</span>
                                  <span className="font-medium text-gray-500">{attendanceStats.absentDays.previousMonth}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Present Days:</span>
                                  <span className="font-medium text-gray-500">0</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Overtime:</span>
                                  <span className="font-medium text-gray-500">{formatMinutes(attendanceStats.overtimeMinutes.previousMonth)}</span>
                                </div>
                                <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <span className="text-gray-700 dark:text-gray-300 font-medium">Attendance Rate:</span>
                                    <span className="font-semibold text-gray-500">0.0%</span>
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">0 of 21 working days</div>
                                </div>
                              </div>
                            </div>

                            {/* Previous Year */}
                            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Previous Year</h4>
                              <div className="space-y-2 text-xs">
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Late Minutes:</span>
                                  <span className="font-medium text-gray-500">{formatMinutes(attendanceStats.lateMinutes.previousYear)}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Absent Days:</span>
                                  <span className="font-medium text-gray-500">{attendanceStats.absentDays.previousYear}</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Present Days:</span>
                                  <span className="font-medium text-gray-500">0</span>
                                </div>
                                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                  <span className="text-gray-600 dark:text-gray-400">Overtime:</span>
                                  <span className="font-medium text-gray-500">{formatMinutes(attendanceStats.overtimeMinutes.previousYear)}</span>
                                </div>
                                <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <span className="text-gray-700 dark:text-gray-300 font-medium">Attendance Rate:</span>
                                    <span className="font-semibold text-gray-500">0.0%</span>
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">0 of 261 working days</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Comparison Tab */}
                        {activeTab === 'comparison' && (
                          <div className="space-y-6">
                            {/* Week Comparison */}
                            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Week Comparison</h4>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                  <div className={`flex justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <span className="text-gray-600 dark:text-gray-400">Late Minutes</span>
                                    <span>This: {formatMinutes(attendanceStats.lateMinutes.thisWeek)} vs Prev: {formatMinutes(attendanceStats.lateMinutes.previousWeek)}</span>
                                  </div>
                                  <div className={`text-right ${isRTL ? 'text-left' : ''}`}>
                                    <span className="text-red-600 font-medium">↑ Worse</span>
                                  </div>
                                </div>
                                <div>
                                  <div className={`flex justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <span className="text-gray-600 dark:text-gray-400">Attendance Rate</span>
                                    <span>40.0% vs 0.0%</span>
                                  </div>
                                  <div className={`text-right ${isRTL ? 'text-left' : ''}`}>
                                    <span className="text-green-600 font-medium">↑ Better</span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Month Comparison */}
                            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Month Comparison</h4>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                  <div className={`flex justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <span className="text-gray-600 dark:text-gray-400">Absent Days</span>
                                    <span>This: {attendanceStats.absentDays.thisMonth} vs Prev: {attendanceStats.absentDays.previousMonth}</span>
                                  </div>
                                  <div className={`text-right ${isRTL ? 'text-left' : ''}`}>
                                    <span className="text-gray-600 font-medium">— Same</span>
                                  </div>
                                </div>
                                <div>
                                  <div className={`flex justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <span className="text-gray-600 dark:text-gray-400">Overtime</span>
                                    <span>{formatMinutes(attendanceStats.overtimeMinutes.thisMonth)} vs {formatMinutes(attendanceStats.overtimeMinutes.previousMonth)}</span>
                                  </div>
                                  <div className={`text-right ${isRTL ? 'text-left' : ''}`}>
                                    <span className="text-blue-600 font-medium">↑ More</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Performance Insights Section */}
                      <div className="border-t border-gray-200 dark:border-gray-700 px-6 py-4 bg-gray-50 dark:bg-gray-700/30">
                        <div className="flex items-center space-x-2 mb-3">
                          <AlertCircle className="h-4 w-4 text-amber-600" />
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white">Performance Insights</h4>
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                          <p>• Current month attendance is below expectations (9.1%)</p>
                          <p>• Late arrivals have increased compared to previous periods</p>
                          <p>• Overtime hours are within acceptable range</p>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className={`flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700 mt-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div>
              {employee && (
                <button
                  type="button"
                  onClick={handleDelete}
                  className={`flex items-center space-x-2 px-4 py-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}
                >
                  <Trash2 className="h-4 w-4" />
                  <span>{t.deleteEmployee}</span>
                </button>
              )}
            </div>
            <div className={`flex space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors font-medium"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium shadow-sm"
              >
                Save Changes
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}